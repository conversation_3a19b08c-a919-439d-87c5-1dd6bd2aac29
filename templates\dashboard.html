{% extends "base.html" %}

{% block title %}Dashboard - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link active" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('giveaways') }}">
                    <i class="fas fa-gift"></i>Giveaways
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Server Info -->
        <div class="server-info">
            <div class="d-flex align-items-center">
                {% if server_icon %}
                <img src="{{ server_icon }}" alt="{{ server_name }}" class="server-avatar me-3" style="width: 80px; height: 80px; border-radius: 50%;">
                {% else %}
                <div class="server-avatar me-3 d-flex align-items-center justify-content-center bg-secondary" style="width: 80px; height: 80px; border-radius: 50%;">
                    <i class="fas fa-server fa-2x text-white"></i>
                </div>
                {% endif %}
                <div>
                    <h2 class="mb-1">{{ server_name }}</h2>
                    <p class="text-muted mb-2">
                        <i class="fas fa-users me-1"></i><span id="memberCount">{{ member_count|number_format if member_count is defined else 'Loading...' }}</span> members
                        <span class="ms-3">
                            <i class="fas fa-id-badge me-1"></i>{{ server_id }}
                        </span>
                    </p>
                    {% if is_configured %}
                    <span class="badge bg-success status-badge">
                        <i class="fas fa-check me-1"></i>Fully Configured
                    </span>
                    {% else %}
                    <span class="badge bg-warning status-badge">
                        <i class="fas fa-exclamation-triangle me-1"></i>Configuration Incomplete
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Configuration Status -->
        {% if not is_configured and final_missing_fields %}
        <div class="alert alert-warning">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <h6 class="mb-0">Configuration Required</h6>
            </div>
            <hr class="my-2">
            <p class="mb-2">Your server is missing some required configuration:</p>
            <ul class="mb-0">
                {% for field in final_missing_fields %}
                <li>{{ field }}</li>
                {% endfor %}
            </ul>
            <div class="mt-3">
                <a href="{{ url_for('configure_repping') }}" class="btn btn-sm btn-warning">
                    <i class="fas fa-cog me-1"></i>Configure Now
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Bot Status -->
        {% if not bot_in_server %}
        <div class="alert alert-danger mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-robot me-2"></i>
                <h6 class="mb-0">Bot Not in Server</h6>
            </div>
            <hr class="my-2">
            <p class="mb-2">The bot is not currently in this server. Please invite the bot to the server to enable all features.</p>
            <div class="mt-2">
                <a href="https://discord.com/oauth2/authorize?client_id={{ bot.user.id }}&permissions=8&scope=bot%20applications.commands" 
                   class="btn btn-sm btn-danger" target="_blank">
                    <i class="fas fa-robot me-1"></i>Invite Bot
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Feature Cards -->
        <div class="row g-4 mt-3">
            <!-- Repping System -->
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>Repping System
                        </h6>
                        {% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-danger">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}
                        <div class="form-check form-switch ms-3">
                            <input class="form-check-input" type="checkbox" id="reppingToggle"
                                   {% if config.get('repping_enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('repping', this.checked)">
                            <label class="form-check-label" for="reppingToggle">
                                <small>{% if config.get('repping_enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body d-flex flex-column">
                        <div class="mb-3">
                            <p class="mb-1"><strong>Status:</strong>
                                {% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}
                                    {% if config.get('repping_enabled', True) %}
                                    <span class="text-success">Active</span>
                                    {% else %}
                                    <span class="text-warning">Configured but Disabled</span>
                                    {% endif %}
                                {% else %}
                                <span class="text-danger">Inactive - Requires Configuration</span>
                                {% endif %}
                                {% if not bot_in_server %}
                                <span class="badge bg-danger ms-2">Bot Required</span>
                                {% endif %}
                            </p>
                            <p class="mb-1"><strong>Trigger Word:</strong>
                                <code>{{ config.trigger_word if config.trigger_word else 'Not set' }}</code>
                            </p>
                            <p class="mb-1"><strong>Role:</strong>
                                {% if config.repping_role_id or config.role_id %}
                                <span class="badge bg-primary">{{ config.repping_role_id or config.role_id }}</span>
                                {% else %}
                                <span class="text-muted">Not set</span>
                                {% endif %}
                            </p>
                            <p class="mb-1"><strong>Channel:</strong>
                                {% if config.repping_channel_id or config.channel_id %}
                                <span class="badge bg-primary">{{ config.repping_channel_id or config.channel_id }}</span>
                                {% else %}
                                <span class="text-muted">Not set</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="mt-auto">
                            <p class="text-muted mb-2">Configure automatic role assignment based on user status.</p>
                            {% if not config.log_channel_id %}
                            <div class="alert alert-warning alert-sm mb-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <small>Requires log channel to be configured first.</small>
                            </div>
                            {% endif %}
                            <a href="{{ url_for('configure_repping') }}" class="btn btn-sm btn-primary w-100">
                                <i class="fas fa-cog me-1"></i>{% if (config.repping_role_id or config.role_id) and (config.repping_channel_id or config.channel_id) and config.trigger_word %}Reconfigure{% else %}Configure{% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vent System -->
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-heart text-danger me-2"></i>Vent System
                        </h6>
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        <div class="form-check form-switch ms-3">
                            <input class="form-check-input" type="checkbox" id="ventToggle"
                                   {% if vent_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('vent', this.checked)">
                            <label class="form-check-label" for="ventToggle">
                                <small>{% if vent_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if vent_settings and vent_settings.vent_channel_id %}
                        <p class="mb-2"><strong>Status:</strong>
                            <span class="{% if vent_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                {% if vent_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                            </span>
                        </p>
                        <p class="mb-3"><strong>Vent Channel ID:</strong> {{ vent_settings.vent_channel_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Allow users to send anonymous messages to a designated channel.</p>
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <small>Requires log channel to be configured first.</small>
                        </div>
                        {% endif %}
                        <a href="{{ url_for('configure_vent') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Temp Voice -->
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-microphone text-info me-2"></i>Temp Voice
                        </h6>
                        {% if tempvoice_settings and tempvoice_settings.interface_channel_id and tempvoice_settings.creator_channel_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if tempvoice_settings and tempvoice_settings.interface_channel_id and tempvoice_settings.creator_channel_id %}
                        <div class="form-check form-switch ms-3">
                            <input class="form-check-input" type="checkbox" id="tempvoiceToggle"
                                   {% if tempvoice_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('tempvoice', this.checked)">
                            <label class="form-check-label" for="tempvoiceToggle">
                                <small>{% if tempvoice_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if tempvoice_settings and tempvoice_settings.interface_channel_id and tempvoice_settings.creator_channel_id %}
                        <p class="mb-2"><strong>Status:</strong>
                            <span class="{% if tempvoice_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                {% if tempvoice_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                            </span>
                        </p>
                        <p class="mb-2"><strong>Interface Channel:</strong> {{ tempvoice_settings.interface_channel_id }}</p>
                        <p class="mb-3"><strong>Creator Channel:</strong> {{ tempvoice_settings.creator_channel_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Temporary voice channels with full user management.</p>
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <small>Requires log channel to be configured first.</small>
                        </div>
                        {% endif %}
                        <a href="{{ url_for('configure_tempvoice') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sticky Messages -->
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-thumbtack text-success me-2"></i>Sticky Messages
                        </h6>
                        {% if sticky_messages %}
                        <span class="badge bg-success">{{ sticky_messages|length }} Active</span>
                        {% else %}
                        <span class="badge bg-secondary">None Active</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if sticky_messages %}
                        <div class="form-check form-switch ms-3">
                            <input class="form-check-input" type="checkbox" id="sticky_messagesToggle"
                                   {% if sticky_messages[0].get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('sticky_messages', this.checked)">
                            <label class="form-check-label" for="sticky_messagesToggle">
                                <small>{% if sticky_messages[0].get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if sticky_messages %}
                        <p class="mb-2"><strong>Status:</strong>
                            <span class="{% if sticky_messages[0].get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                {% if sticky_messages[0].get('enabled', True) %}Active{% else %}Disabled{% endif %}
                            </span>
                        </p>
                        <p class="mb-3">{{ sticky_messages|length }} sticky message(s) configured</p>
                        {% else %}
                        <p class="text-muted mb-3">Persistent messages that automatically repost.</p>
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <small>Requires log channel to be configured first.</small>
                        </div>
                        {% endif %}
                        <a href="{{ url_for('configure_sticky_messages') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- DM Support -->
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-ticket-alt text-primary me-2"></i>DM Support
                        </h6>
                        {% if dm_support_settings and dm_support_settings.category_id and dm_support_settings.support_role_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if dm_support_settings and dm_support_settings.category_id and dm_support_settings.support_role_id %}
                        <div class="form-check form-switch ms-3">
                            <input class="form-check-input" type="checkbox" id="dm_supportToggle"
                                   {% if dm_support_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('dm_support', this.checked)">
                            <label class="form-check-label" for="dm_supportToggle">
                                <small>{% if dm_support_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if dm_support_settings and dm_support_settings.category_id and dm_support_settings.support_role_id %}
                        <p class="mb-2"><strong>Status:</strong>
                            <span class="{% if dm_support_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                {% if dm_support_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                            </span>
                        </p>
                        <p class="mb-2"><strong>Category ID:</strong> {{ dm_support_settings.category_id }}</p>
                        <p class="mb-3"><strong>Support Role:</strong> {{ dm_support_settings.support_role_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">DM-based support ticket system with admin responses.</p>
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <small>Requires log channel to be configured first.</small>
                        </div>
                        {% endif %}
                        <a href="{{ url_for('configure_dm_support') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>

            <!-- Gender Verification -->
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt text-secondary me-2"></i>Gender Verification
                        </h6>
                        {% if gender_verification_settings and gender_verification_settings.channel_id and gender_verification_settings.category_id %}
                        <span class="badge bg-success">Configured</span>
                        {% else %}
                        <span class="badge bg-secondary">Not Configured</span>
                        {% endif %}

                        <!-- Enable/Disable Toggle -->
                        {% if gender_verification_settings and gender_verification_settings.channel_id and gender_verification_settings.category_id %}
                        <div class="form-check form-switch ms-3">
                            <input class="form-check-input" type="checkbox" id="gender_verificationToggle"
                                   {% if gender_verification_settings.get('enabled', True) %}checked{% endif %}
                                   onchange="toggleFeature('gender_verification', this.checked)">
                            <label class="form-check-label" for="gender_verificationToggle">
                                <small>{% if gender_verification_settings.get('enabled', True) %}Enabled{% else %}Disabled{% endif %}</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if gender_verification_settings and gender_verification_settings.channel_id and gender_verification_settings.category_id %}
                        <p class="mb-2"><strong>Status:</strong>
                            <span class="{% if gender_verification_settings.get('enabled', True) %}text-success{% else %}text-warning{% endif %}">
                                {% if gender_verification_settings.get('enabled', True) %}Active{% else %}Disabled{% endif %}
                            </span>
                        </p>
                        <p class="mb-2"><strong>Channel ID:</strong> {{ gender_verification_settings.channel_id }}</p>
                        <p class="mb-3"><strong>Category ID:</strong> {{ gender_verification_settings.category_id }}</p>
                        {% else %}
                        <p class="text-muted mb-3">Manual verification system with ticket support.</p>
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <small>Requires log channel to be configured first.</small>
                        </div>
                        {% endif %}
                        <a href="{{ url_for('configure_gender_verification') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Configure
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Toggle feature enable/disable
function toggleFeature(feature, enabled) {
    // Get CSRF token from meta tag or generate it
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '{{ csrf_token() }}';

    fetch('/api/toggle-feature', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            feature: feature,
            enabled: enabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the label text
            const toggle = document.getElementById(feature + 'Toggle');
            const label = toggle.nextElementSibling.querySelector('small');
            label.textContent = enabled ? 'Enabled' : 'Disabled';

            // Show success message
            showToast('Feature ' + (enabled ? 'enabled' : 'disabled') + ' successfully!', 'success');

            // Refresh the page after a short delay to update status
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            // Revert the toggle if the request failed
            const toggle = document.getElementById(feature + 'Toggle');
            toggle.checked = !enabled;
            showToast('Failed to update feature: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Revert the toggle if the request failed
        const toggle = document.getElementById(feature + 'Toggle');
        toggle.checked = !enabled;
        showToast('An error occurred while updating the feature.', 'error');
    });
}

// Simple toast notification function
function showToast(message, type) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(toast);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
