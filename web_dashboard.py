import asyncio
import json
import logging
import os
from datetime import datetime, timedelta, timezone
from functools import wraps
from typing import Optional, Dict, Any, List

from dotenv import load_dotenv
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory, g
from flask_wtf.csrf import CSRFProtect, generate_csrf
import jwt
import requests
import discord

# Load environment variables first
load_dotenv()

# Then import local modules
try:
    from oauth2 import get_oauth_url, get_token, get_user_info, get_user_guilds, create_jwt_token, login_required, get_license_keys
    from database import DatabaseManager
except ImportError as e:
    logging.error(f"Failed to import required modules: {e}")
    raise

# Load configuration from environment variables
MONGO_URL = os.getenv('MONGO_URL')
SECRET_KEY = os.getenv('SECRET_KEY')

if not MONGO_URL or not SECRET_KEY:
    raise ValueError("Missing required environment variables. Please check your .env file.")

# Discord OAuth2 configuration
DISCORD_CLIENT_ID = os.getenv('DISCORD_CLIENT_ID')
DISCORD_CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET')
DISCORD_REDIRECT_URI = os.getenv('DISCORD_REDIRECT_URI')

if not all([DISCORD_CLIENT_ID, DISCORD_CLIENT_SECRET]):
    raise ValueError("Missing required Discord OAuth2 configuration. Set DISCORD_CLIENT_ID and DISCORD_CLIENT_SECRET environment variables.")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = SECRET_KEY

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Add CSRF token to all templates
@app.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf)

# Add custom Jinja2 filters
@app.template_filter('number_format')
def number_format(value):
    """Format number with commas as thousand separators"""
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return value

@app.template_filter('time_ago')
def time_ago(value):
    """Convert datetime to time ago format"""
    if not value:
        return "Never"

    try:
        from datetime import datetime, timezone

        # Ensure value is timezone-aware
        if isinstance(value, datetime):
            if value.tzinfo is None:
                value = value.replace(tzinfo=timezone.utc)
        else:
            # If it's a string, try to parse it
            if isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    return "Unknown"
            else:
                return "Unknown"

        now = datetime.now(timezone.utc)
        diff = now - value

        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "Just now"
    except Exception:
        return "Unknown"

# Initialize database
db = DatabaseManager(MONGO_URL)

# Global bot reference (will be set when integrated with main.py)
bot = None

def set_bot_reference(bot_instance):
    """Set the bot reference for accessing Discord data"""
    global bot
    bot = bot_instance

@app.before_request
def initialize_database():
    """Initialize database connection"""
    if not hasattr(initialize_database, 'initialized'):
        try:
            db.connect()
            logger.info("Web dashboard database connected successfully")
            initialize_database.initialized = True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")

# Use the login_required decorator from oauth2.py
# We'll keep the old require_auth for backward compatibility
def require_auth(f):
    """Decorator to require authentication"""
    # Use wraps to preserve the original function's metadata
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        return f(*args, **kwargs)
    return decorated_function

# Global error handlers
@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': 'Page not found'}), 404
    flash('The page you requested was not found.', 'error')
    return redirect(url_for('index'))

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}", exc_info=True)
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': 'An internal server error occurred'}), 500
    flash('An internal server error occurred. Please try again later.', 'error')
    return redirect(url_for('index'))

@app.errorhandler(403)
def forbidden_error(error):
    """Handle 403 errors"""
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': 'Access forbidden'}), 403
    flash('You do not have permission to access this resource.', 'error')
    return redirect(url_for('index'))

def handle_route_error(e, route_name, redirect_to='index'):
    """Common error handler for routes"""
    logger.error(f"Error in {route_name} route: {str(e)}", exc_info=True)
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': f'An error occurred in {route_name}. Please try again.'}), 500
    flash(f'An error occurred while loading {route_name}. Please try again.', 'error')
    return redirect(url_for(redirect_to))

def get_server_info(server_id: int) -> Optional[Dict[str, Any]]:
    """Get server information from Discord API"""
    try:
        # Try to get from session guilds if available
        guilds = session.get('guilds', [])
        user_servers = session.get('user_servers', [])
        server_id_str = str(server_id)

        # Try to find in guilds
        guild = next((g for g in guilds if str(g.get('id')) == server_id_str), None)
        if guild:
            return {
                'id': int(guild['id']),
                'name': guild.get('name', 'Unknown Server'),
                'icon': guild.get('icon'),
                'member_count': guild.get('approximate_member_count', guild.get('member_count', 0)),
                'owner_id': guild.get('owner_id')
            }

        # Try to find in user_servers
        user_server = next((s for s in user_servers if str(s.get('id')) == server_id_str), None)
        if user_server:
            return {
                'id': int(user_server['id']),
                'name': user_server.get('name', 'Unknown Server'),
                'icon': user_server.get('icon'),
                'member_count': user_server.get('member_count', 0),
                'owner_id': user_server.get('owner_id')
            }

        # If not in session, try to fetch from Discord API
        token = session.get('token', {}).get('access_token')
        if token:
            headers = {
                'Authorization': f'Bearer {token}'
            }
            response = requests.get(
                f'https://discord.com/api/v10/users/@me/guilds/{server_id}',
                headers=headers
            )
            if response.status_code == 200:
                guild = response.json()
                return {
                    'id': int(guild['id']),
                    'name': guild.get('name', 'Unknown Server'),
                    'icon': guild.get('icon'),
                    'member_count': guild.get('approximate_member_count', guild.get('member_count', 0)),
                    'owner_id': guild.get('owner_id')
                }

        logger.warning(f"Could not fetch server info for {server_id}")
        return {
            'id': int(server_id),
            'name': 'Unknown Server',
            'icon': None,
            'member_count': 0,
            'owner_id': None
        }
    except Exception as e:
        logger.error(f"Error in get_server_info: {e}")
        return {
            'id': int(server_id),
            'name': 'Unknown Server',
            'icon': None,
            'member_count': 0,
            'owner_id': None
        }

def get_user_servers(license_key: str) -> List[Dict[str, Any]]:
    """Get all servers associated with a license key"""
    # Get license key info
    key_info = db.get_license_key_by_key(license_key)
    if not key_info or not key_info.get('redeemed'):
        return []
    
    # Get server info if key is redeemed to a server
    servers = []
    if key_info.get('server_id'):
        server_info = get_server_info(key_info['server_id'])
        if server_info:
            servers.append(server_info)
    
    return servers

@app.route('/')
def index():
    """Home page - redirect to dashboard if authenticated, otherwise show welcome page"""
    if 'token' in session:
        # Check if user has any license keys
        try:
            user_id = session.get('user_id')
            if user_id:
                license_keys = db.get_user_license_keys(user_id, include_disabled=True)
                if license_keys:
                    return redirect(url_for('select_server'))
                return redirect(url_for('no_license'))
        except Exception as e:
            logger.error(f"Error checking license keys: {e}")
            # If there's an error, let the user log in again
            session.clear()
            return redirect(url_for('logout'))
    return render_template('index.html', discord_auth_url=get_oauth_url())

@app.route('/dashboard')
@require_auth
def dashboard():
    """Main dashboard page"""
    try:
        # Get server ID from session
        server_id = session.get('server_id')
        if not server_id:
            logger.warning("No server_id in session, redirecting to server selection")
            return redirect(url_for('select_server'))
        
        # Get user ID from session
        user_id = session.get('user_id')
        if not user_id:
            logger.warning("No user_id in session, redirecting to login")
            return redirect(url_for('logout'))
        
        logger.info(f"Loading dashboard for server {server_id} (user: {user_id})")
        
        # Convert server_id to int if it's a string
        try:
            server_id_int = int(server_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid server_id format: {server_id}, error: {e}")
            flash('Invalid server ID. Please select a server again.', 'error')
            return redirect(url_for('select_server'))
        
        # Get server info from session or API
        server_info = get_server_info(server_id_int)
        if not server_info:
            logger.warning(f"Server info not found for server_id: {server_id}")
            flash('Server information could not be loaded. Please try selecting the server again.', 'error')
            return redirect(url_for('select_server'))
        
        # Get server configuration from database
        try:
            config = db.get_server_config(server_id_int) or {}
            logger.info(f"Loaded server config: {config}")
            
            # Handle legacy field names for backward compatibility
            if 'role_id' in config and 'repping_role_id' not in config:
                config['repping_role_id'] = config['role_id']
            if 'channel_id' in config and 'repping_channel_id' not in config:
                config['repping_channel_id'] = config['channel_id']
                
        except Exception as e:
            logger.error(f"Error loading server config: {e}", exc_info=True)
            config = {}
            flash('Error loading server configuration. Some features may not work correctly.', 'warning')
        
        # Define required fields and their display names
        field_display_names = {
            'repping_role_id': 'Repping Role',
            'repping_channel_id': 'Repping Channel',
            'trigger_word': 'Trigger Word',
            'vent_channel_id': 'Vent Channel',
            'tempvoice_interface_channel_id': 'Temp Voice Interface',
            'tempvoice_creator_channel_id': 'Temp Voice Creator'
        }
        
        # Always required fields for basic functionality
        required_fields = ['repping_role_id', 'repping_channel_id', 'trigger_word']
        
        # Check which features are enabled and require configuration
        enabled_features = []
        if config.get('vent_enabled', False):
            enabled_features.append('vent')
            required_fields.append('vent_channel_id')
            
        if config.get('tempvoice_enabled', False):
            enabled_features.append('tempvoice')
            required_fields.extend(['tempvoice_interface_channel_id', 'tempvoice_creator_channel_id'])
        
        # Find missing required fields
        missing_fields = []
        for field in required_fields:
            if not config.get(field):
                display_name = field_display_names.get(field, field.replace('_', ' ').title())
                missing_fields.append(display_name)
        
        # Determine if configuration is complete
        is_configured = len(missing_fields) == 0
        
        # Log configuration status for debugging
        logger.info(f"Server {server_id} configuration check:")
        logger.info(f"- Enabled features: {', '.join(enabled_features) if enabled_features else 'None'}")
        logger.info(f"- Missing required fields: {', '.join(missing_fields) if missing_fields else 'None'}")
        logger.info(f"- Configuration status: {'Complete' if is_configured else 'Incomplete'}")
        
        # For display in the template
        final_missing_fields = missing_fields
        
        # Get member count
        member_count = 0
        bot_in_server = False
        if bot:
            try:
                guild = bot.get_guild(server_id_int)
                if guild:
                    member_count = guild.member_count
                    bot_in_server = guild.me is not None
                    logger.info(f"Found server in bot's guilds with {member_count} members")
            except Exception as e:
                logger.error(f"Error getting member count or bot status: {e}", exc_info=True)
        
        # Update session with latest info
        session['server_name'] = server_info.get('name', 'Unknown Server')
        session['member_count'] = member_count
        session['trigger_word'] = config.get('trigger_word', '/leakin')
        session.modified = True
        
        # Get user's servers for the sidebar
        user_servers = session.get('user_servers', [])
        
        # Prepare feature configurations

        # Extract repping and vent configurations
        repping_config = {
            'repping_role_id': config.get('repping_role_id', config.get('role_id')),
            'repping_channel_id': config.get('repping_channel_id', config.get('channel_id')),
            'trigger_word': config.get('trigger_word', '/leakin')
        }

        # Get additional feature settings from their respective collections
        vent_settings = db.get_vent_settings(server_id_int)
        tempvoice_settings = db.get_tempvoice_settings(server_id_int)
        sticky_messages = db.get_all_sticky_messages(server_id_int)
        dm_support_settings = db.get_dm_support_settings(server_id_int)
        gender_verification_settings = db.get_gender_verification_settings(server_id_int)

        # Check configuration status for each feature
        missing_fields = []

        # Check repping system (always required)
        if not config.get('repping_role_id') and not config.get('role_id'):
            missing_fields.append('repping_role_id')
        if not config.get('repping_channel_id') and not config.get('channel_id'):
            missing_fields.append('repping_channel_id')
        if not config.get('trigger_word'):
            missing_fields.append('trigger_word')

        # Check vent system (only if configured)
        if config.get('vent_channel_id') and not config.get('vent_channel_id'):
            missing_fields.append('vent_channel_id')

        # Check tempvoice system (only if configured)
        if tempvoice_settings and (not tempvoice_settings.get('interface_channel_id') or not tempvoice_settings.get('creator_channel_id')):
            if not tempvoice_settings.get('interface_channel_id'):
                missing_fields.append('tempvoice_interface_channel_id')
            if not tempvoice_settings.get('creator_channel_id'):
                missing_fields.append('tempvoice_creator_channel_id')

        is_configured = len(missing_fields) == 0
        
        # Format missing fields for display
        formatted_missing = []
        field_display_names = {
            'repping_role_id': 'Repping Role',
            'repping_channel_id': 'Repping Channel',
            'trigger_word': 'Trigger Word',
            'vent_channel_id': 'Vent Channel',
            'tempvoice_interface_channel_id': 'Temp Voice Interface Channel',
            'tempvoice_creator_channel_id': 'Temp Voice Creator Channel'
        }
        
        for field in missing_fields:
            formatted_missing.append(field_display_names.get(field, field.replace('_', ' ').title()))
        
        # Construct server icon URL
        server_icon_url = None
        if server_info.get('icon'):
            server_icon_url = f"https://cdn.discordapp.com/icons/{server_id}/{server_info['icon']}.png"

        # Prepare template context
        template_context = {
            'server_id': server_id_int,  # Use integer version for consistency
            'server_name': server_info.get('name', 'Unknown Server'),
            'server_icon': server_icon_url,
            'member_count': member_count,
            'trigger_word': config.get('trigger_word', '/leakin'),
            'user_servers': user_servers,
            'server_info': server_info,
            'config': config,  # Pass the full config instead of just repping_config
            'vent_settings': vent_settings,
            'tempvoice_settings': tempvoice_settings,
            'sticky_messages': sticky_messages,
            'dm_support_settings': dm_support_settings,
            'gender_verification_settings': gender_verification_settings,
            'is_configured': is_configured,
            'missing_fields': missing_fields,  # Use the list of display names
            'final_missing_fields': formatted_missing,  # Also pass the formatted missing fields
            'settings': config,
            'bot_in_server': bot_in_server
        }
        
        logger.info(f"Rendering dashboard with context: {template_context}")
        return render_template('dashboard.html', **template_context)
                            
    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}", exc_info=True)
        flash('An error occurred while loading the dashboard. Please try again.', 'error')
        return redirect(url_for('select_server'))

@app.route('/login')
def login():
    """Redirect to Discord OAuth2 login"""
    if 'token' in session:
        return redirect(url_for('dashboard'))
    return redirect(get_oauth_url())

@app.route('/oauth/callback')
def oauth_callback():
    """Handle OAuth2 callback from Discord"""
    if 'code' not in request.args:
        flash('No authorization code received from Discord', 'error')
        return redirect(url_for('login'))
    
    try:
        # Exchange the authorization code for an access token
        token = get_token(request.args['code'])
        access_token = token['access_token']
        
        # Get user information
        user_info = get_user_info(access_token)
        
        # Store user info in session
        discord_user = {
            'id': user_info['id'],
            'username': user_info['username'],
            'discriminator': user_info.get('discriminator', '0'),
            'avatar': user_info.get('avatar'),
            'email': user_info.get('email')
        }
        
        # Create JWT token for session management
        session_token = create_jwt_token(discord_user)
        session['token'] = session_token
        session['discord_user'] = discord_user
        
        # Make session permanent
        session.permanent = True
        
        # Get user's guilds
        guilds = get_user_guilds(access_token)
        session['guilds'] = guilds
        
        # Debug: Log user info
        logger.info(f"Authenticated user: {user_info['username']} (ID: {user_info['id']})")
        
        # Get user's active license keys from database (excluding disabled ones)
        logger.info(f"Fetching license keys for user ID: {user_info['id']}")
        license_keys = db.get_user_license_keys(user_info['id'], include_disabled=True)  # Include disabled to show them with visual indicator
        logger.info(f"Found {len(license_keys)} license keys for user")
        
        # Debug: Log guilds and license keys
        logger.info(f"User is in {len(guilds)} guilds")
        logger.info(f"User has {len(license_keys)} license keys")
        
        # Create a mapping of server IDs to guild data for faster lookup
        guilds_map = {g['id']: g for g in guilds if 'id' in g}
        logger.info(f"Created guilds map with {len(guilds_map)} entries")
        
        # Find all servers where the user has a license key
        user_servers = []
        for key in license_keys:
            try:
                if not key or 'server_id' not in key:
                    logger.warning(f"Skipping invalid license key (missing server_id): {key}")
                    continue
                    
                server_id = key['server_id']
                server_id_str = str(server_id)
                logger.info(f"Processing license key for server ID: {server_id_str}")
                
                # Find the guild in the user's guilds
                user_guild = guilds_map.get(server_id_str)
                
                if not user_guild:
                    logger.warning(f"User is not in server {server_id_str} (or bot doesn't have access)")
                    continue
                
                # Get server info from the guild data
                server_info = {
                    'id': int(server_id_str),
                    'name': user_guild.get('name', 'Unknown Server'),
                    'icon': user_guild.get('icon'),
                    'member_count': user_guild.get('approximate_member_count', 0),
                    'owner_id': user_guild.get('owner_id')
                }
                
                # Check if the key is disabled
                is_disabled = key.get('disabled', False)
                logger.info(f"License key for server {server_id_str} - Disabled: {is_disabled}")
                
                server_data = {
                    'id': int(server_id_str),
                    'name': server_info['name'],
                    'icon': f"https://cdn.discordapp.com/icons/{server_id_str}/{server_info['icon']}.png" if server_info.get('icon') else None,
                    'member_count': server_info['member_count'],
                    'license_key': key['key'],
                    'disabled': is_disabled,
                    'is_owner': int(user_info['id']) == int(user_guild.get('owner_id', 0))
                }
                
                logger.info(f"Adding server to user_servers: {server_data['name']} (ID: {server_data['id']})")
                user_servers.append(server_data)
                
            except Exception as e:
                logger.error(f"Error processing license key {key.get('key', 'unknown')}: {str(e)}", exc_info=True)
                continue
        
        # Store user's servers with valid licenses in session
        session['user_servers'] = user_servers
        
        # If user has no servers with valid licenses, redirect to no_license
        if not user_servers:
            logger.info(f'User {user_info["username"]} has no servers with valid licenses')
            return redirect(url_for('no_license'))
        
        # If user has only one server, automatically select it
        if len(user_servers) == 1:
            server = user_servers[0]
            session['server_id'] = server['id']
            session['license_key'] = server['license_key']
            logger.info(f'Auto-selected server {server["name"]} for user {user_info["username"]}')
            return redirect(url_for('dashboard'))
        
        # If we get here, user has multiple servers - show selection
        return redirect(url_for('select_server'))
        
    except Exception as e:
        logger.error(f'OAuth callback error: {str(e)}', exc_info=True)
        flash('Failed to authenticate with Discord. Please try again.', 'error')
        return redirect(url_for('login'))

@app.route('/select-server')
@login_required
def select_server():
    """Page to select a server if user has access to multiple"""
    logger.info("Entering select_server route")
    
    # Get user ID from session
    user_id = session.get('discord_user', {}).get('id')
    if not user_id:
        logger.error("No user ID found in session")
        flash('Session expired. Please log in again.', 'error')
        return redirect(url_for('logout'))
    
    logger.info(f"User ID from session: {user_id}")
    
    # Get fresh license keys from database
    try:
        license_keys = db.get_user_license_keys(user_id, include_disabled=True)
        logger.info(f"Found {len(license_keys)} license keys for user {user_id}")
        
        if not license_keys:
            logger.warning(f"No license keys found for user {user_id}")
            flash('No license keys found for your account.', 'warning')
            return redirect(url_for('no_license'))
            
        # Get user's guilds from session
        guilds = session.get('guilds', [])
        logger.info(f"User is in {len(guilds)} guilds")
        
        # Log guild information for debugging
        logger.info(f"Processing {len(guilds)} guilds from Discord API")
        
        # Create mapping of server IDs to guild data
        guilds_map = {}
        for i, g in enumerate(guilds, 1):
            try:
                guild_id = g.get('id')
                if not guild_id:
                    logger.warning(f"Guild at index {i} has no ID, skipping")
                    continue
                    
                guild_name = g.get('name', 'Unknown Guild')
                member_count = g.get('approximate_member_count', g.get('member_count', 0))
                icon_hash = g.get('icon')
                
                # Log guild details
                logger.debug(f"Guild {i}: {guild_name} (ID: {guild_id})")
                
                # Ensure member_count is an integer
                try:
                    member_count = int(member_count) if member_count else 0
                except (ValueError, TypeError):
                    member_count = 0
                    
                # Add member_count to guild data if not present
                if 'approximate_member_count' not in g and member_count > 0:
                    g['approximate_member_count'] = member_count
                
                logger.debug(f"  - Members: {member_count}")
                logger.debug(f"  - Icon: {'Yes' if icon_hash else 'No'}")
                logger.debug(f"  - Owner ID: {g.get('owner_id', 'N/A')}")
                
                # Add to map with processed data
                guilds_map[guild_id] = g
                
            except Exception as e:
                logger.error(f"Error processing guild {i}: {str(e)}")
                logger.debug(f"Problematic guild data: {g}", exc_info=True)
        
        logger.info(f"Created guilds map with {len(guilds_map)} valid guilds")
        
        logger.info(f"Created guilds map with {len(guilds_map)} entries")
        
        # Process each license key
        user_servers = []
        for key in license_keys:
            try:
                if not key or 'server_id' not in key:
                    logger.warning(f"Skipping invalid license key (missing server_id): {key}")
                    continue
                    
                server_id = key['server_id']
                server_id_str = str(server_id)
                logger.info(f"Processing license key for server ID: {server_id_str}")
                
                # Find the guild in the user's guilds
                guild = guilds_map.get(server_id_str)
                
                if not guild:
                    logger.warning(f"User is not in server {server_id_str} (or bot doesn't have access)")
                    continue
                
                try:
                    # Get server info from the guild data
                    guild_id = str(guild.get('id', server_id_str))
                    owner_id = guild.get('owner_id')
                    server_name = guild.get('name', 'Unknown Server')
                    
                    # Debug log the guild data structure
                    logger.debug(f"Processing guild data for {server_name} (ID: {guild_id})")
                    logger.debug(f"Guild keys: {list(guild.keys())}")
                    
                    # Handle icon - try multiple possible locations
                    icon_hash = None
                    icon_sources = ['icon', 'icon_hash', 'guild_icon', 'icon_url']
                    for source in icon_sources:
                        if guild.get(source):
                            icon_hash = guild[source]
                            break
                    
                    # If we have an icon URL, extract the hash
                    if icon_hash and ('http://' in icon_hash or 'https://' in icon_hash):
                        # Extract hash from URL like https://cdn.discordapp.com/icons/1234567890/abc123.png
                        parts = icon_hash.split('/')
                        if len(parts) >= 2:
                            icon_hash = parts[-1].split('.')[0]
                    
                    # Get member count with multiple fallbacks
                    member_count = 1
                    try:
                        # Try different possible field names for member count
                        member_fields = ['approximate_member_count', 'member_count', 'members_count', 'size']
                        for field in member_fields:
                            if guild.get(field) is not None:
                                try:
                                    member_count = max(1, int(guild[field]))
                                    logger.debug(f"Found member count in {field}: {member_count}")
                                    break
                                except (ValueError, TypeError):
                                    logger.debug(f"Error parsing {field} for member count")
                        
                        # If still no valid count, try to fetch from Discord API
                        if member_count <= 1 and os.getenv("BOT_TOKEN"):
                            try:
                                headers = {'Authorization': f'Bot {os.getenv("BOT_TOKEN")}'}
                                response = requests.get(
                                    f'https://discord.com/api/v10/guilds/{guild_id}?with_counts=true',
                                    headers=headers,
                                    timeout=5
                                )
                                if response.status_code == 200:
                                    guild_data = response.json()
                                    api_member_count = guild_data.get('approximate_member_count')
                                    if api_member_count is not None:
                                        member_count = max(1, int(api_member_count))
                                        logger.debug(f"Fetched member count via API: {member_count}")
                            except Exception as e:
                                logger.error(f"Error fetching member count from API: {e}")
                                
                    except Exception as e:
                        logger.error(f"Error getting member count: {e}")
                        member_count = 1  # Fallback to 1
                    
                    logger.info(f"Server {server_name} (ID: {guild_id}) - Members: {member_count}, Icon: {'Yes' if icon_hash else 'No'}")
                    
                    # Check if the key is disabled
                    is_disabled = key.get('disabled', False)
                    logger.info(f"License key for server {guild_id} - Disabled: {is_disabled}")
                    
                    # Safely handle owner comparison
                    is_owner = False
                    if owner_id is not None:
                        try:
                            is_owner = str(user_id) == str(owner_id)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error comparing user IDs: {e}")
                    
                    # Build the icon URL if we have an icon hash
                    icon_url = None
                    if icon_hash:
                        # Clean the icon hash
                        clean_icon_hash = icon_hash.split('?')[0].split('#')[0]
                        icon_url = f"https://cdn.discordapp.com/icons/{guild_id}/{clean_icon_hash}.png"
                        
                        # Try with different extensions if .png fails
                        icon_extensions = ['png', 'jpg', 'jpeg', 'webp', 'gif']
                        if not any(icon_url.lower().endswith(ext) for ext in icon_extensions):
                            icon_url = f"https://cdn.discordapp.com/icons/{guild_id}/{clean_icon_hash}.png"
                    
                    server_data = {
                        'id': int(guild_id),
                        'name': server_name,
                        'icon': icon_url,
                        'member_count': max(1, member_count),  # Ensure at least 1 member
                        'license_key': key['key'],
                        'disabled': is_disabled,
                        'is_owner': is_owner
                    }
                    
                    logger.debug(f"Created server data: {server_data}")
                    
                except Exception as e:
                    logger.error(f"Error processing guild data: {str(e)}", exc_info=True)
                    # Skip this server if there's an error
                    continue
                
                logger.info(f"Adding server to user_servers: {server_data['name']} (ID: {server_data['id']})")
                user_servers.append(server_data)
                
            except Exception as e:
                logger.error(f"Error processing license key {key.get('key', 'unknown')}: {str(e)}", exc_info=True)
                continue
        
        if not user_servers:
            logger.warning(f"No servers found with valid license keys for user {user_id}")
            flash('No servers found with active license keys. Please contact support if you believe this is an error.', 'warning')
            return redirect(url_for('no_license'))
            
        # Update session with fresh server data
        session['user_servers'] = user_servers
        
        # If only one server, redirect to dashboard
        if len(user_servers) == 1:
            server = user_servers[0]
            session['server_id'] = server['id']
            session['license_key'] = server.get('license_key')
            logger.info(f"Auto-selecting single server: {server['name']} (ID: {server['id']})")
            return redirect(url_for('dashboard'))
            
        # Sort servers by name, with disabled servers at the end
        sorted_servers = sorted(user_servers, key=lambda x: (x.get('disabled', False), x.get('name', '').lower()))
        logger.info(f"Rendering server selection page with {len(sorted_servers)} servers")
        return render_template('server_selection.html', servers=sorted_servers, username=session.get('discord_user', {}).get('username', 'User'))
        
    except Exception as e:
        logger.error(f'Error in select_server: {str(e)}', exc_info=True)
        flash('An error occurred while loading your servers. Please try again.', 'error')
        return redirect(url_for('logout'))

@app.route('/select-server/<server_id>', methods=['GET', 'POST'])
@login_required
def select_server_id(server_id):
    """Set the selected server in session"""
    try:
        logger.info(f"Starting server selection for server_id: {server_id}")
        
        # Ensure we have a valid session
        if 'user_id' not in session:
            logger.warning("No user_id in session, attempting to restore from token")
            if 'token' in session:
                try:
                    # Try to restore user_id from token if possible
                    from oauth2 import SECRET_KEY
                    import jwt
                    payload = jwt.decode(session['token'], SECRET_KEY, algorithms=['HS256'])
                    session['user_id'] = str(payload['user_id'])
                    logger.info(f"Restored user_id from token: {session['user_id']}")
                except Exception as e:
                    logger.error(f"Failed to restore user_id from token: {str(e)}")
                    if request.is_json:
                        return jsonify({'error': 'Session expired. Please log in again.', 'logout': True}), 401
                    return redirect(url_for('logout'))
            else:
                logger.warning("No token found in session")
                if request.is_json:
                    return jsonify({'error': 'Session expired. Please log in again.', 'logout': True}), 401
                return redirect(url_for('logout'))
        
        # Ensure session is permanent
        session.permanent = True
        
        # Convert server_id to string for consistency
        server_id = str(server_id)
        user_id = session.get('user_id')
        logger.info(f"Selecting server {server_id} for user {user_id}")
        
        # Verify the user has access to this server
        user_servers = session.get('user_servers', [])
        logger.info(f"User {user_id} has access to {len(user_servers)} servers")
        
        if not any(str(s.get('id')) == server_id for s in user_servers):
            logger.warning(f"User {user_id} attempted to access unauthorized server {server_id}")
            if request.is_json:
                return jsonify({'error': 'You do not have permission to access this server.', 'redirect': url_for('select_server')}), 403
            flash('You do not have permission to access this server.', 'error')
            return redirect(url_for('select_server'))
        
        # Refresh user servers if not in session
        if 'user_servers' not in session or not session['user_servers']:
            try:
                logger.info(f"Refreshing server list for user {user_id}")
                # Get user's license keys
                license_keys = db.get_user_license_keys(user_id)
                logger.info(f"Found {len(license_keys)} license keys for user {user_id}")
                
                if not license_keys:
                    logger.warning(f"No license keys found for user {user_id}")
                    if request.is_json:
                        return jsonify({'error': 'No license keys found.', 'redirect': url_for('no_license')}), 403
                    return redirect(url_for('no_license'))
                
                # Get guilds from Discord API
                guilds = get_user_guilds(session.get('token', {}).get('access_token'))
                logger.info(f"Found {len(guilds or [])} guilds from Discord API")
                
                if not guilds:
                    logger.warning(f"No guilds found for user {user_id}")
                    if request.is_json:
                        return jsonify({'error': 'No servers found. Make sure the bot is in at least one server.'}), 404
                    flash('No servers found. Make sure the bot is in at least one server.', 'error')
                    return redirect(url_for('select_server'))
                
                # Process guilds and match with license keys
                user_servers = []
                for guild in guilds:
                    guild_id = str(guild.get('id'))
                    for key in license_keys:
                        if str(key.get('server_id')) == guild_id:
                            # Get server configuration for trigger word
                            server_config = db.get_server_config(guild_id) or {}
                            logger.debug(f"Found server config for {guild_id}: {server_config}")
                            
                            user_servers.append({
                                'id': guild_id,
                                'name': guild.get('name', 'Unknown Server'),
                                'icon': guild.get('icon'),
                                'member_count': guild.get('approximate_member_count', 0),
                                'license_key': key.get('key'),
                                'trigger_word': server_config.get('trigger_word', '/leakin')
                            })
                            break
                
                if not user_servers:
                    logger.warning(f"No servers found with valid license keys for user {user_id}")
                    if request.is_json:
                        return jsonify({'error': 'No servers found with valid license keys.', 'redirect': url_for('no_license')}), 403
                    return redirect(url_for('no_license'))
                
                session['user_servers'] = user_servers
                session['guilds'] = guilds
                logger.info(f"Refreshed {len(user_servers)} servers for user {user_id}")
                
            except Exception as e:
                logger.error(f"Error refreshing user servers: {str(e)}", exc_info=True)
                if request.is_json:
                    return jsonify({'error': 'Failed to load server information. Please try again.', 'redirect': url_for('select_server')}), 500
                flash('Failed to load server information. Please try again.', 'error')
                return redirect(url_for('select_server'))
        
        # Verify the selected server is in user's servers
        user_servers = session.get('user_servers', [])
        selected_server = next((s for s in user_servers if str(s.get('id')) == server_id), None)
        
        if not selected_server:
            logger.warning(f"Server {server_id} not found in user's servers")
            if request.is_json:
                return jsonify({'error': 'Server not found in your servers.', 'redirect': url_for('select_server')}), 404
            flash('Server not found in your servers.', 'error')
            return redirect(url_for('select_server'))
        
        # Get fresh server info from Discord API
        try:
            server_info = get_server_info(server_id)
            if not server_info:
                raise Exception("Failed to fetch server info from Discord")
                
            # Update session with selected server
            session['server_id'] = server_id
            session['server_name'] = server_info.get('name', selected_server.get('name', 'Unknown Server'))
            session['server_icon'] = server_info.get('icon')
            session['member_count'] = server_info.get('member_count', 0)
            session['trigger_word'] = selected_server.get('trigger_word', '/leakin')
            
            # Ensure we have the license key for this server
            if 'license_key' not in selected_server and 'license_key' in session:
                session['license_key'] = selected_server.get('license_key')
            
            logger.info(f"Successfully selected server {server_id} ({session['server_name']}) for user {user_id}")
            
            # Ensure session is saved before responding
            session.modified = True
            
            # Clear any existing flash messages to prevent them from persisting
            if '_flashes' in session:
                session.pop('_flashes', None)
            
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'redirect': url_for('dashboard'),
                    'server': {
                        'id': server_id,
                        'name': session['server_name'],
                        'icon': server_info.get('icon'),
                        'member_count': server_info.get('member_count', 0)
                    }
                })
                
            return redirect(url_for('dashboard'))
            
        except Exception as e:
            logger.error(f"Error getting server info: {str(e)}", exc_info=True)
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'error': 'Failed to get server information. Please try again.',
                    'redirect': url_for('select_server')
                }), 500
            flash('Failed to get server information. Please try again.', 'error')
            return redirect(url_for('select_server'))
        
    except Exception as e:
        logger.error(f'Error selecting server {server_id}: {str(e)}', exc_info=True)
        if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'error': 'An error occurred while selecting the server. Please try again.',
                'logout': isinstance(e, (KeyError, AttributeError))  # Suggest logout for session errors
            }), 500
        flash('An error occurred while selecting the server. Please try again.', 'error')
        return redirect(url_for('select_server'))

@app.route('/no-license')
def no_license():
    """Show no license page when user has no valid licenses"""
    # Check if user is logged in
    if 'discord_user' not in session:
        flash('Please log in to view this page', 'info')
        return redirect(url_for('login'))
    
    # Double-check if user really has no servers with licenses
    if 'user_servers' in session and session['user_servers']:
        # Verify at least one server has a valid license
        for server in session['user_servers']:
            if server.get('license_key'):
                return redirect(url_for('select_server'))
    
    # Get user info for display
    user_info = session.get('discord_user', {})
    username = user_info.get('username', 'User')
    
    # Get support server invite from config or use default
    support_server = os.getenv('SUPPORT_SERVER_INVITE')
    
    # Log the access attempt
    logger.info(f'User {username} (ID: {user_info.get("id", "unknown")} accessed no_license page')
    
    return render_template('no_license.html', 
                         support_server=support_server,
                         username=username)

@app.route('/logout')
def logout():
    """Logout and clear session with security measures"""
    # Log the logout attempt
    user_info = session.get('discord_user', {})
    username = user_info.get('username', 'Unknown User')
    user_id = user_info.get('id', 'Unknown ID')
    
    # Log the successful logout
    logger.info(f'User {username} (ID: {user_id}) logged out successfully')
    
    # Clear the session data
    session.clear()
    
    # Clear the session cookie by setting it to expire
    session.modified = True
    
    # Add security headers to prevent caching of sensitive pages
    response = redirect(url_for('index'))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    
    flash('You have been successfully logged out.', 'success')
    return response

@app.route('/configure/repping', methods=['GET', 'POST'])
@login_required
def configure_repping():
    """Configure the repping system"""
    if 'server_id' not in session:
        return redirect(url_for('select_server'))

    server_id = session['server_id']

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    server_info = get_server_info(server_id_int)

    if not server_info:
        flash('Could not fetch server information. Please try again.', 'danger')
        return redirect(url_for('dashboard'))

    # Get current configuration
    config = db.get_server_config(server_id_int) or {}

    # Check if log channel is configured (required for repping system)
    if not config.get('log_channel_id'):
        flash('Log channel must be configured first. Please set it in Settings before configuring the repping system.', 'error')
        return redirect(url_for('settings'))
    
    if request.method == 'POST':
        # Get form data
        repping_role_id = request.form.get('repping_role')
        repping_channel_id = request.form.get('repping_channel')
        trigger_word = request.form.get('trigger_word', '/leakin').strip()
        
        # Validate trigger word
        if not trigger_word:
            flash('Please enter a trigger word', 'danger')
            return redirect(url_for('configure_repping'))
            
        # Prepare updates
        updates = {
            'repping_role_id': repping_role_id,
            'repping_channel_id': repping_channel_id,
            'trigger_word': trigger_word,
            'updated_at': datetime.now(timezone.utc)
        }
        
        # Save to database
        if db.update_server_config(server_id_int, updates):
            flash('Repping system configuration saved successfully!', 'success')
            
            # Update session with new trigger word
            session['trigger_word'] = trigger_word
            session.modified = True
            
            # Check if we need to redirect to dashboard or stay on page
            if 'save_and_continue' in request.form:
                return redirect(url_for('dashboard'))
            return redirect(url_for('configure_repping'))
        else:
            flash('Failed to save configuration. Please try again.', 'danger')
    
    # Get current values or defaults (check both new and legacy field names)
    repping_config = {
        'role_id': config.get('repping_role_id') or config.get('role_id', ''),
        'channel_id': config.get('repping_channel_id') or config.get('channel_id', ''),
        'trigger_word': config.get('trigger_word', '/leakin')
    }

    # Get server info for the template
    server_icon = f"https://cdn.discordapp.com/icons/{server_id_int}/{server_info.get('icon')}.png" if server_info.get('icon') else None
    
    return render_template('configure_repping.html',
                         server_name=server_info.get('name', 'Unknown Server'),
                         server_icon=server_icon,
                         config=repping_config,
                         title='Configure Repping System')

@app.route('/configure/vent', methods=['GET', 'POST'])
@require_auth
def configure_vent():
    """Configure vent system"""
    if 'server_id' not in session:
        return redirect(url_for('select_server'))

    server_id = session['server_id']

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Check if log channel is configured (required for vent system)
    config = db.get_server_config(server_id_int)
    if not config or not config.get('log_channel_id'):
        flash('Log channel must be configured first. Please set it in Settings before configuring the vent system.', 'error')
        return redirect(url_for('settings'))

    server_info = get_server_info(server_id_int)

    if not server_info:
        flash('Could not fetch server information. Please try again.', 'danger')
        return redirect(url_for('dashboard'))
        
    # Get current configuration
    config = db.get_server_config(server_id) or {}

    if request.method == 'POST':
        vent_channel_id = request.form.get('vent_channel_id', '').strip()

        if not vent_channel_id:
            flash('Vent channel ID is required', 'error')
            return redirect(url_for('configure_vent'))

        try:
            vent_channel_id = int(vent_channel_id)
        except ValueError:
            flash('Invalid channel ID', 'error')
            return redirect(url_for('configure_vent'))

        # Check if log channel is configured
        config = db.get_server_config(server_id)
        if not config or not config.get('log_channel_id'):
            flash('Log channel must be configured first. Please set it in the repping system configuration.', 'error')
            return redirect(url_for('configure_vent'))

        # Save vent settings
        success = db.set_vent_settings(server_id_int, vent_channel_id)

        if success:
            flash('Vent system configured successfully!', 'success')
        else:
            flash('Failed to save vent configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    vent_settings = db.get_vent_settings(server_id_int)
    config = db.get_server_config(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_vent.html',
                         vent_settings=vent_settings,
                         config=config,
                         server_info=server_info)

@app.route('/configure/tempvoice', methods=['GET', 'POST'])
@require_auth
def configure_tempvoice():
    """Configure temp voice system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Check if log channel is configured (required for tempvoice system)
    config = db.get_server_config(server_id_int)
    if not config or not config.get('log_channel_id'):
        flash('Log channel must be configured first. Please set it in Settings before configuring the temp voice system.', 'error')
        return redirect(url_for('settings'))

    if request.method == 'POST':
        interface_channel_id = request.form.get('interface_channel_id', '').strip()
        creator_channel_id = request.form.get('creator_channel_id', '').strip()
        default_user_limit = request.form.get('default_user_limit', '').strip()

        if not all([interface_channel_id, creator_channel_id]):
            flash('Interface and creator channel IDs are required', 'error')
            return redirect(url_for('configure_tempvoice'))

        try:
            interface_channel_id = int(interface_channel_id)
            creator_channel_id = int(creator_channel_id)
            default_user_limit = int(default_user_limit) if default_user_limit else None
        except ValueError:
            flash('Invalid channel ID or user limit', 'error')
            return redirect(url_for('configure_tempvoice'))

        # Save tempvoice settings
        success = db.set_tempvoice_settings(server_id_int, interface_channel_id, creator_channel_id, default_user_limit)

        if success:
            flash('Temp voice system configured successfully!', 'success')
        else:
            flash('Failed to save temp voice configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    tempvoice_settings = db.get_tempvoice_settings(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_tempvoice.html',
                         tempvoice_settings=tempvoice_settings,
                         server_info=server_info)

@app.route('/settings', methods=['GET', 'POST'])
@require_auth
def settings():
    """Server settings page including ignored users and other configurations"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'add_ignored_user':
            user_id = request.form.get('user_id', '').strip()

            if not user_id:
                flash('User ID is required', 'error')
                return redirect(url_for('settings'))

            try:
                user_id = int(user_id)
            except ValueError:
                flash('Invalid user ID', 'error')
                return redirect(url_for('settings'))

            success = db.add_ignored_user(server_id_int, user_id)
            if success:
                flash('User added to ignored list successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "User added to ignored list",
                    f"User ID: {user_id}",
                    "config"
                )
            else:
                flash('Failed to add user to ignored list', 'error')

        elif action == 'remove_ignored_user':
            user_id = request.form.get('user_id', '').strip()

            try:
                user_id = int(user_id)
            except ValueError:
                flash('Invalid user ID', 'error')
                return redirect(url_for('settings'))

            success = db.remove_ignored_user(server_id_int, user_id)
            if success:
                flash('User removed from ignored list successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "User removed from ignored list",
                    f"User ID: {user_id}",
                    "config"
                )
            else:
                flash('Failed to remove user from ignored list', 'error')

        elif action == 'set_log_channel':
            log_channel_id = request.form.get('log_channel_id', '').strip()

            if not log_channel_id:
                flash('Log channel is required', 'error')
                return redirect(url_for('settings'))

            try:
                log_channel_id = int(log_channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('settings'))

            # Update server config with log channel
            config_update = {'log_channel_id': log_channel_id}
            success = db.save_server_config(server_id_int, config_update)

            if success:
                flash('Log channel updated successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Log channel configured",
                    f"Channel ID: {log_channel_id}",
                    "config",
                    log_channel_id
                )
            else:
                flash('Failed to update log channel', 'error')

        return redirect(url_for('settings'))

    # GET request - show settings page
    config = db.get_server_config(server_id_int)
    server_info = get_server_info(server_id_int)
    ignored_users = config.get('ignored_users', []) if config else []

    return render_template('settings.html',
                         config=config,
                         ignored_users=ignored_users,
                         server_info=server_info)

@app.route('/configure/dm-support', methods=['GET', 'POST'])
@require_auth
def configure_dm_support():
    """Configure DM support system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Check if log channel is configured (required for DM support system)
    config = db.get_server_config(server_id_int)
    if not config or not config.get('log_channel_id'):
        flash('Log channel must be configured first. Please set it in Settings before configuring the DM support system.', 'error')
        return redirect(url_for('settings'))

    if request.method == 'POST':
        category_id = request.form.get('category_id', '').strip()
        support_role_id = request.form.get('support_role_id', '').strip()

        if not all([category_id, support_role_id]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_dm_support'))

        try:
            category_id = int(category_id)
            support_role_id = int(support_role_id)
        except ValueError:
            flash('Invalid category ID or role ID', 'error')
            return redirect(url_for('configure_dm_support'))

        # Check if log channel is configured
        config = db.get_server_config(server_id_int)
        if not config or not config.get('log_channel_id'):
            flash('Log channel must be configured first. Please set it in Settings.', 'error')
            return redirect(url_for('configure_dm_support'))

        # Create support-logs channel name
        support_logs_channel_id = None  # Will be created by the bot if needed

        # Save DM support settings
        success = db.set_dm_support_settings(server_id_int, category_id, support_role_id, support_logs_channel_id)

        if success:
            flash('DM support system configured successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id_int,
                session['user_id'],
                "Web Dashboard User",
                "DM support system configured",
                f"Category ID: {category_id}, Support Role ID: {support_role_id}",
                "config"
            )
        else:
            flash('Failed to save DM support configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    dm_support_settings = db.get_dm_support_settings(server_id_int)
    config = db.get_server_config(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_dm_support.html',
                         dm_support_settings=dm_support_settings,
                         config=config,
                         server_info=server_info)

@app.route('/configure/gender-verification', methods=['GET', 'POST'])
@require_auth
def configure_gender_verification():
    """Configure gender verification system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Check if log channel is configured (required for gender verification system)
    config = db.get_server_config(server_id_int)
    if not config or not config.get('log_channel_id'):
        flash('Log channel must be configured first. Please set it in Settings before configuring the gender verification system.', 'error')
        return redirect(url_for('settings'))

    if request.method == 'POST':
        channel_id = request.form.get('channel_id', '').strip()
        category_id = request.form.get('category_id', '').strip()
        support_role_id = request.form.get('support_role_id', '').strip()
        paper_text = request.form.get('paper_text', '').strip()

        if not all([channel_id, category_id, support_role_id, paper_text]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_gender_verification'))

        try:
            channel_id = int(channel_id)
            category_id = int(category_id)
            support_role_id = int(support_role_id)
        except ValueError:
            flash('Invalid channel ID, category ID, or role ID', 'error')
            return redirect(url_for('configure_gender_verification'))

        # Save gender verification settings
        success = db.set_gender_verification_settings(server_id_int, channel_id, category_id, support_role_id, paper_text)

        if success:
            flash('Gender verification system configured successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id_int,
                session['user_id'],
                "Web Dashboard User",
                "Gender verification system configured",
                f"Channel ID: {channel_id}, Category ID: {category_id}, Support Role ID: {support_role_id}",
                "config"
            )
        else:
            flash('Failed to save gender verification configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    gender_verification_settings = db.get_gender_verification_settings(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_gender_verification.html',
                         gender_verification_settings=gender_verification_settings,
                         server_info=server_info)

@app.route('/api/toggle-feature', methods=['POST'])
@require_auth
def toggle_feature():
    """API endpoint to enable/disable features"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'success': False, 'error': 'Invalid server ID'}), 400

    try:
        data = request.get_json()
        feature = data.get('feature')
        enabled = data.get('enabled', True)

        if not feature:
            return jsonify({'success': False, 'error': 'Feature name is required'}), 400

        # Check if log channel is configured (required for all features)
        config = db.get_server_config(server_id_int)
        if not config or not config.get('log_channel_id'):
            return jsonify({
                'success': False,
                'error': 'Log channel must be configured first. Please set it in Settings.'
            }), 400

        success = False

        if feature == 'repping':
            # Update repping system enabled status in server config
            if config.get('repping_role_id') or config.get('role_id'):
                success = db.update_server_config_field(server_id_int, 'repping_enabled', enabled)
            else:
                return jsonify({'success': False, 'error': 'Repping system not configured'}), 400

        elif feature == 'vent':
            # Update vent settings
            vent_settings = db.get_vent_settings(server_id_int)
            if vent_settings:
                success = db.update_vent_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'Vent system not configured'}), 400

        elif feature == 'tempvoice':
            # Update tempvoice settings
            tempvoice_settings = db.get_tempvoice_settings(server_id_int)
            if tempvoice_settings:
                success = db.update_tempvoice_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'TempVoice system not configured'}), 400

        elif feature == 'dm_support':
            # Update DM support settings
            dm_support_settings = db.get_dm_support_settings(server_id_int)
            if dm_support_settings:
                success = db.update_dm_support_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'DM Support system not configured'}), 400

        elif feature == 'sticky_messages':
            # Update sticky messages enabled status
            sticky_messages = db.get_all_sticky_messages(server_id_int)
            if sticky_messages:
                success = db.update_sticky_messages_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'No sticky messages configured'}), 400

        elif feature == 'gender_verification':
            # Update gender verification settings
            gender_verification_settings = db.get_gender_verification_settings(server_id_int)
            if gender_verification_settings:
                success = db.update_gender_verification_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'Gender verification system not configured'}), 400

        else:
            return jsonify({'success': False, 'error': 'Unknown feature'}), 400

        if success:
            # Log the action
            db.log_bot_activity(
                server_id_int,
                session['user_id'],
                "Web Dashboard User",
                f"{feature.title()} system {'enabled' if enabled else 'disabled'}",
                f"Feature toggled via dashboard",
                "config"
            )

            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Failed to update feature settings'}), 500

    except Exception as e:
        logger.error(f"Error toggling feature: {e}", exc_info=True)
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/configure/sticky-messages', methods=['GET', 'POST'])
@require_auth
def configure_sticky_messages():
    """Configure sticky messages"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Check if log channel is configured (required for sticky messages)
    config = db.get_server_config(server_id_int)
    if not config or not config.get('log_channel_id'):
        flash('Log channel must be configured first. Please set it in Settings before configuring sticky messages.', 'error')
        return redirect(url_for('settings'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'create':
            channel_id = request.form.get('channel_id', '').strip()
            content = request.form.get('content', '').strip()

            if not all([channel_id, content]):
                flash('Channel and content are required', 'error')
                return redirect(url_for('configure_sticky_messages'))

            try:
                channel_id = int(channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('configure_sticky_messages'))

            # Create sticky message
            success = db.create_sticky_message(server_id_int, channel_id, content)

            if success:
                flash('Sticky message created successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message created",
                    f"Channel ID: {channel_id}, Content length: {len(content)} characters",
                    "sticky",
                    channel_id
                )
            else:
                flash('Failed to create sticky message', 'error')

        elif action == 'remove':
            channel_id = request.form.get('channel_id', '').strip()

            try:
                channel_id = int(channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('configure_sticky_messages'))

            # Remove sticky message
            success = db.remove_sticky_message(server_id_int, channel_id)

            if success:
                flash('Sticky message removed successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message removed",
                    f"Channel ID: {channel_id}",
                    "sticky"
                )
            else:
                flash('Failed to remove sticky message', 'error')

        return redirect(url_for('configure_sticky_messages'))

    # GET request - show form
    sticky_messages = db.get_all_sticky_messages(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_sticky_messages.html',
                         sticky_messages=sticky_messages,
                         server_info=server_info)

# Keep the old automod route for backward compatibility
@app.route('/configure/automod')
@require_auth
def configure_automod():
    """Redirect to settings page"""
    return redirect(url_for('settings'))

@app.after_request
def add_cors_headers(response):
    response.headers['Access-Control-Allow-Origin'] = request.headers.get('Origin', '*')
    response.headers['Access-Control-Allow-Credentials'] = 'true'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, X-CSRFToken'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    return response

@app.route('/api/server-info')
@require_auth
def api_server_info():
    """API endpoint to get server information"""
    try:
        server_id = int(session.get('server_id', 0))
        if not server_id:
            return jsonify({'error': 'No server selected'}), 400
            
        server_info = get_server_info(server_id)
        if not server_info:
            return jsonify({'error': 'Server not found'}), 404

        # Initialize response with basic server info
        response_data = {
            'id': str(server_id),
            'name': server_info.get('name', 'Unknown Server'),
            'icon': server_info.get('icon'),
            'member_count': 0,
            'icon_url': f"https://cdn.discordapp.com/icons/{server_id}/{server_info.get('icon')}.png" if server_info.get('icon') else None
        }

        # Get member count and other guild info from bot
        if bot:
            guild = bot.get_guild(server_id)
            if guild:
                # Get member count directly
                try:
                    # Use the cached member count - it's updated by Discord.py
                    response_data['member_count'] = guild.member_count
                    
                    # Update server info with guild data
                    response_data.update({
                        'name': guild.name,
                        'icon': str(guild.icon) if guild.icon else None,
                        'icon_url': str(guild.icon.url) if guild.icon and hasattr(guild.icon, 'url') else None,
                        'owner_id': str(guild.owner_id) if hasattr(guild, 'owner_id') else None
                    })
                except Exception as e:
                    logging.warning(f"Couldn't fetch guild data: {e}")
                    # Use the cached member count if available
                    response_data['member_count'] = server_info.get('member_count', 0)
        
        # Add configuration from database
        config = db.get_server_config(server_id) or {}
        response_data['config'] = config
            
        return jsonify(response_data)
        
    except Exception as e:
        logging.error(f'Error in api_server_info: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/channels')
@require_auth
def api_channels():
    """API endpoint to get server channels"""
    try:
        server_id = session.get('server_id')
        if not server_id:
            logger.error("No server_id in session")
            return jsonify({'error': 'No server selected'}), 400

        # Convert server_id to int for consistency
        try:
            server_id = int(server_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid server_id format: {server_id}, error: {e}")
            return jsonify({'error': 'Invalid server ID format'}), 400

        # Check if bot is available
        if not bot:
            logger.warning("Bot instance not available, returning fallback response")
            return jsonify({
                'error': 'Bot not connected',
                'fallback': True,
                'message': 'Please enter channel IDs manually. The bot is not currently connected.'
            }), 503

        # Get guild from bot
        guild = bot.get_guild(server_id)
        if not guild:
            logger.error(f"Guild not found for server_id: {server_id}")
            return jsonify({
                'error': 'Server not found',
                'fallback': True,
                'message': 'Bot is not in this server or server not found. Please enter channel IDs manually.'
            }), 404

        try:
            channels = []
            for channel in guild.channels:
                try:
                    channel_info = {
                        'id': str(channel.id),
                        'name': channel.name,
                        'type': '',
                        'category': channel.category.name if channel.category and hasattr(channel.category, 'name') else None
                    }

                    # Check channel type using discord.py types
                    if isinstance(channel, discord.TextChannel):
                        channel_info['type'] = 'text'
                        channels.append(channel_info)
                    elif isinstance(channel, discord.VoiceChannel):
                        channel_info['type'] = 'voice'
                        channels.append(channel_info)
                    elif isinstance(channel, discord.CategoryChannel):
                        channel_info['type'] = 'category'
                        channel_info['category'] = None
                        channels.append(channel_info)
                    elif isinstance(channel, discord.ForumChannel):
                        channel_info['type'] = 'forum'
                        channels.append(channel_info)

                except Exception as e:
                    logger.warning(f'Error processing channel {getattr(channel, "id", "unknown")}: {str(e)}')
                    continue

            logger.info(f"Successfully loaded {len(channels)} channels for server {server_id}")
            return jsonify(channels)

        except Exception as e:
            logger.error(f'Error in api_channels processing: {str(e)}', exc_info=True)
            return jsonify({
                'error': 'Failed to process channels',
                'fallback': True,
                'message': 'Error loading channels from Discord. Please enter channel IDs manually.'
            }), 500

    except Exception as e:
        logger.error(f'Unexpected error in api_channels: {str(e)}', exc_info=True)
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/roles')
@require_auth
def api_roles():
    """API endpoint to get server roles (only roles below bot's highest role)"""
    try:
        server_id = session.get('server_id')
        if not server_id:
            logger.error("No server_id in session")
            return jsonify({'error': 'No server selected'}), 400

        # Convert server_id to int for consistency
        try:
            server_id = int(server_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid server_id format: {server_id}, error: {e}")
            return jsonify({'error': 'Invalid server ID format'}), 400

        # Check if bot is available
        if not bot:
            logger.warning("Bot instance not available, returning fallback response")
            return jsonify({
                'error': 'Bot not connected',
                'fallback': True,
                'message': 'Please enter role IDs manually. The bot is not currently connected.'
            }), 503

        # Get guild from bot
        guild = bot.get_guild(server_id)
        if not guild:
            logger.error(f"Guild not found for server_id: {server_id}")
            return jsonify({
                'error': 'Server not found',
                'fallback': True,
                'message': 'Bot is not in this server or server not found. Please enter role IDs manually.'
            }), 404

        try:
            roles = []
            bot_member = guild.me
            if not bot_member:
                logger.error(f"Bot member not found in guild {server_id}")
                return jsonify({
                    'error': 'Bot not found in server',
                    'fallback': True,
                    'message': 'Bot is not properly configured in this server. Please enter role IDs manually.'
                }), 404

            bot_highest_position = max([r.position for r in bot_member.roles]) if bot_member.roles else 0

            for role in guild.roles:
                try:
                    # Skip @everyone role and roles above bot's highest role
                    if role.is_default() or role.position >= bot_highest_position:
                        continue

                    roles.append({
                        'id': str(role.id),
                        'name': role.name,
                        'color': str(role.color),
                        'position': role.position,
                        'permissions': role.permissions.value
                    })
                except Exception as e:
                    logger.warning(f'Error processing role {getattr(role, "id", "unknown")}: {str(e)}')
                    continue

            # Sort by position (highest first)
            roles.sort(key=lambda x: x['position'], reverse=True)
            logger.info(f"Successfully loaded {len(roles)} roles for server {server_id}")
            return jsonify(roles)

        except Exception as e:
            logger.error(f'Error in api_roles processing: {str(e)}', exc_info=True)
            return jsonify({
                'error': 'Failed to process roles',
                'fallback': True,
                'message': 'Error loading roles from Discord. Please enter role IDs manually.'
            }), 500

    except Exception as e:
        logger.error(f'Unexpected error in api_roles: {str(e)}', exc_info=True)
        return jsonify({'error': 'Internal server error'}), 500

def _get_members(guild):
    """Helper function to get members with proper async handling"""
    try:
        # Ensure the guild is chunked
        if not guild.chunked:
            # Run the coroutine in the bot's event loop
            future = asyncio.run_coroutine_threadsafe(guild.chunk(), bot.loop)
            future.result(timeout=10)  # Wait up to 10 seconds for chunking to complete
        return guild.members
    except Exception as e:
        logger.error(f"Error fetching members for guild {guild.id}: {e}")
        return []

@app.route('/api/members')
@require_auth
def api_members():
    """API endpoint to get server members"""
    server_id = session.get('server_id')
    search_query = request.args.get('q', '').lower()
    
    if not server_id:
        return jsonify({'error': 'No server selected'}), 400

    if not bot:
        return jsonify({'error': 'Bot not connected'}), 500

    try:
        guild = bot.get_guild(server_id)
        if not guild:
            return jsonify({'error': 'Server not found'}), 404

        # Get members synchronously
        members = _get_members(guild)
        
        # Process members
        member_list = []
        for member in members:
            if member.bot:
                continue
                
            # Get avatar URL with fallback to default avatar
            avatar_url = str(member.avatar.url) if member.avatar else f'https://cdn.discordapp.com/embed/avatars/{int(member.discriminator) % 5}.png'
            
            member_list.append({
                'id': str(member.id),
                'name': f'{member.name}#{member.discriminator}',
                'display_name': member.display_name,
                'avatar_url': avatar_url
            })
        
        # Apply search filter if query provided
        if search_query:
            member_list = [
                member for member in member_list 
                if (search_query in member['name'].lower() or 
                     search_query in member['display_name'].lower())
            ]
        
        # Sort by display name
        member_list.sort(key=lambda x: x['display_name'].lower())
        
        return jsonify(member_list)
    except Exception as e:
        logging.error(f'Error fetching members: {str(e)}', exc_info=True)
        return jsonify({'error': 'Failed to fetch members. Please try again.'}), 500

@app.route('/logs')
@require_auth
def logs():
    """Dashboard logs viewer"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Get filter parameters
    category = request.args.get('category', '')
    limit = min(int(request.args.get('limit', 100)), 500)  # Max 500 logs

    # Get logs from database
    logs = db.get_bot_logs(server_id_int, limit=limit, category=category if category else None)

    # Get live statistics for activity breakdown
    stats = db.get_log_statistics(server_id_int, days=0)  # Use 0 to indicate all-time stats

    # Get server info
    server_info = get_server_info(server_id_int)

    return render_template('logs.html',
                         logs=logs,
                         stats=stats,
                         server_info=server_info,
                         current_category=category,
                         current_limit=limit)

@app.route('/api/logs')
@require_auth
def api_logs():
    """API endpoint for logs (for AJAX updates)"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid server ID'}), 400

    category = request.args.get('category', '')
    limit = min(int(request.args.get('limit', 100)), 500)

    logs = db.get_bot_logs(server_id_int, limit=limit, category=category if category else None)

    # Convert datetime objects to strings for JSON serialization
    for log in logs:
        if 'timestamp' in log:
            log['timestamp'] = log['timestamp'].isoformat()

    return jsonify(logs)

@app.route('/stats')
@require_auth
def stats():
    """Dashboard stats viewer"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Get server statistics (with live Discord data if bot is available)
    server_stats = db.get_server_statistics(server_id_int, bot)

    # Get live log statistics for activity breakdown
    log_stats = db.get_log_statistics(server_id_int, days=7)  # Default to last 7 days

    # Get current repping users (with live Discord data if bot is available)
    current_repping_users = db.get_current_repping_users(server_id_int, bot)

    # Get top repping users
    top_repping_users = db.get_top_repping_users(server_id_int, limit=6, bot=bot)

    # Get recent repping activity
    recent_repping_activity = db.get_recent_repping_activity(server_id_int, limit=25)

    # Get active temp voice channels with user info
    active_temp_channels = db.get_active_temp_channels_with_users(server_id_int)

    # Get server info
    server_info = get_server_info(server_id_int)

    return render_template('stats.html',
                         server_stats=server_stats,
                         log_stats=log_stats,
                         server_info=server_info,
                         current_repping_users=current_repping_users,
                         top_repping_users=top_repping_users,
                         recent_repping_activity=recent_repping_activity,
                         active_temp_channels=active_temp_channels)

@app.route('/api/stats-data')
@require_auth
def api_stats_data():
    """API endpoint for stats data with time period filtering"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid server ID'}), 400

    # Get time period from query params
    period = request.args.get('period', '7')  # Default to 7 days

    try:
        days = int(period)
    except ValueError:
        days = 7

    # Get log statistics for the specified period
    log_stats = db.get_log_statistics(server_id_int, days=days)

    return jsonify({
        'success': True,
        'log_stats': log_stats,
        'period': days
    })

@app.route('/api/cleanup-logs', methods=['POST'])
@require_auth
def api_cleanup_logs():
    """API endpoint to manually clean up old logs"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid server ID'}), 400

    try:
        deleted_count = db.cleanup_all_server_logs(server_id_int, max_logs=100)

        # Log the cleanup action
        db.log_bot_activity(
            server_id_int,
            session['user_id'],
            "Web Dashboard User",
            "Manual log cleanup performed",
            f"Deleted {deleted_count} old log entries",
            "config"
        )

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'Successfully cleaned up {deleted_count} old log entries'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/giveaways')
@require_auth
def giveaways():
    """Giveaways management page"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Get server info
    server_info = get_server_info(server_id_int)
    logger.info(f"Server info for giveaways page: {server_info}")

    # Get real-time member count from bot if available
    if bot:
        guild = bot.get_guild(server_id_int)
        if guild:
            server_info['member_count'] = guild.member_count
            logger.info(f"Updated member count from bot: {guild.member_count}")
        else:
            logger.warning(f"Guild {server_id_int} not found in bot's guilds")

    # Get all server giveaways with debugging
    now_utc = datetime.now(timezone.utc)
    
    # Debug: Log what we're getting from the database
    logger.info(f"Fetching giveaways for server {server_id_int}")
    all_giveaways = db.get_server_giveaways(server_id_int) or []
    logger.info(f"Raw giveaways from DB: {all_giveaways}")
    
    # Process and validate giveaways
    processed_giveaways = []
    active_giveaways_count = 0
    
    for i, giveaway in enumerate(all_giveaways):
        try:
            logger.info(f"Processing giveaway {i}: {giveaway}")
            
            # Ensure giveaway is a dictionary
            if not isinstance(giveaway, dict):
                logger.warning(f"Giveaway {i} is not a dict: {type(giveaway)}")
                continue

            # Debug: Print all available keys
            logger.info(f"Giveaway {i} keys: {list(giveaway.keys())}")

            # Get channel name if bot is available
            channel_name = 'Unknown Channel'
            channel_id = giveaway.get('channel_id')
            if channel_id and bot:
                guild = bot.get_guild(server_id_int)
                if guild:
                    channel = guild.get_channel(int(channel_id))
                    if channel:
                        channel_name = f"#{channel.name}"

            # Get winner list if available
            winner_list = giveaway.get('winner_list', giveaway.get('selected_winners', []))
            if not isinstance(winner_list, list):
                winner_list = []

            # Ensure required fields exist with defaults
            processed = {
                'id': str(giveaway.get('_id', '')),
                'item': str(giveaway.get('item', giveaway.get('prize', 'Unknown Item'))),  # Try both 'item' and 'prize'
                'requirements': str(giveaway.get('requirements', giveaway.get('description', 'No requirements specified'))),  # Try both
                'ended': bool(giveaway.get('ended', False)),
                'end_time': giveaway.get('end_time'),
                'channel_id': str(giveaway.get('channel_id', '')),
                'channel_name': channel_name,
                'host_user_id': str(giveaway.get('host_user_id', giveaway.get('host_id', ''))),  # Try both
                'winner_count': int(giveaway.get('winners', giveaway.get('winner_count', 1))),  # Number of winners to select
                'winner_list': winner_list,  # Actual list of selected winners
                'entries': list(giveaway.get('entries', giveaway.get('participants', []))),  # Try both
                'created_at': giveaway.get('created_at', giveaway.get('start_time', now_utc))  # Try both
            }

            logger.info(f"Processed giveaway {i}: {processed}")
            logger.info(f"Winner data - count: {processed['winner_count']}, list: {processed['winner_list']}")

            # Ensure end_time is timezone-aware
            if processed['end_time'] and hasattr(processed['end_time'], 'tzinfo') and processed['end_time'].tzinfo is None:
                processed['end_time'] = processed['end_time'].replace(tzinfo=timezone.utc)

            # Determine if giveaway is active
            is_active = (not processed['ended'] and
                        (not processed['end_time'] or processed['end_time'] > now_utc))

            # Set status for template
            if processed['ended']:
                processed['status'] = 'ended'
            elif is_active:
                processed['status'] = 'active'
            else:
                processed['status'] = 'expired'

            if is_active:
                active_giveaways_count += 1

            processed_giveaways.append(processed)

        except Exception as e:
            logger.error(f"Error processing giveaway {i}: {e}", exc_info=True)
            continue
    
    logger.info(f"Final processed giveaways: {processed_giveaways}")
    
    # Sort giveaways: active first (by end time), then inactive (by creation time desc)
    def sort_key(giveaway):
        is_active = (not giveaway['ended'] and 
                    (not giveaway['end_time'] or giveaway['end_time'] > now_utc))
        
        if is_active:
            # Active giveaways: sort by end time (soonest first)
            return (0, giveaway['end_time'] or datetime.max.replace(tzinfo=timezone.utc))
        else:
            # Inactive giveaways: sort by creation time (newest first)
            created_at = giveaway['created_at']
            if hasattr(created_at, 'timestamp'):
                return (1, -created_at.timestamp())
            else:
                return (1, 0)
    
    processed_giveaways.sort(key=sort_key)
    
    # Keep only the latest 10 giveaways and clean up old ones
    if len(processed_giveaways) > 10:
        # Get IDs of giveaways to delete (older than the 10 most recent)
        giveaways_to_delete = processed_giveaways[10:]
        for old_giveaway in giveaways_to_delete:
            try:
                if hasattr(db, 'delete_giveaway'):
                    db.delete_giveaway(old_giveaway['id'])
                else:
                    logger.warning("delete_giveaway method not found in database")
            except Exception as e:
                logger.error(f"Error deleting old giveaway {old_giveaway['id']}: {e}")
        
        # Keep only the 10 most recent
        processed_giveaways = processed_giveaways[:10]

    # Get server members for host selection and channels
    members = []
    channels = []

    if bot:
        guild = bot.get_guild(server_id_int)
        if guild:
            try:
                # Get channels first
                channels = [
                    {'id': channel.id, 'name': channel.name}
                    for channel in guild.text_channels
                    if channel.permissions_for(guild.me).send_messages
                ]
                channels.sort(key=lambda x: x['name'].lower())
                logger.info(f"Fetched {len(channels)} channels for giveaways page")

                # Get members - try different approaches
                logger.info(f"Guild {guild.name} has {len(guild.members)} cached members")

                # Try to get members from cache first
                all_members = [member for member in guild.members if not member.bot]

                # If we don't have many members cached, try to chunk
                if len(all_members) < 5:
                    try:
                        logger.info("Attempting to chunk guild members...")
                        future = asyncio.run_coroutine_threadsafe(guild.chunk(), bot.loop)
                        future.result(timeout=15)
                        logger.info(f"After chunking: {len(guild.members)} total members")
                        all_members = [member for member in guild.members if not member.bot]
                    except Exception as chunk_error:
                        logger.error(f"Error chunking guild: {chunk_error}")

                # If still no members, try to fetch them directly with higher limit
                if len(all_members) == 0:
                    try:
                        logger.info("Attempting to fetch members directly...")
                        async def fetch_members():
                            members_list = []
                            async for member in guild.fetch_members(limit=1000):  # Increased limit
                                if not member.bot:
                                    members_list.append(member)
                            return members_list

                        future = asyncio.run_coroutine_threadsafe(fetch_members(), bot.loop)
                        all_members = future.result(timeout=30)  # Increased timeout
                        logger.info(f"Fetched {len(all_members)} members directly")
                    except Exception as fetch_error:
                        logger.error(f"Error fetching members directly: {fetch_error}")

                # If we still have very few members, try one more approach
                if len(all_members) < 5:
                    try:
                        logger.info("Attempting alternative member fetching...")
                        async def fetch_all_members():
                            members_list = []
                            async for member in guild.fetch_members(limit=None):  # Fetch all members
                                if not member.bot:
                                    members_list.append(member)
                                if len(members_list) >= 500:  # Reasonable limit
                                    break
                            return members_list

                        future = asyncio.run_coroutine_threadsafe(fetch_all_members(), bot.loop)
                        all_members = future.result(timeout=45)
                        logger.info(f"Alternative fetch got {len(all_members)} members")
                    except Exception as alt_fetch_error:
                        logger.error(f"Error with alternative member fetching: {alt_fetch_error}")

                # Format members for dropdown
                members = []
                for member in all_members:
                    try:
                        display_name = getattr(member, 'display_name', None) or getattr(member, 'name', 'Unknown User')
                        username = getattr(member, 'name', 'unknown')
                        discriminator = getattr(member, 'discriminator', '0000')

                        # Ensure we have valid strings
                        display_name = str(display_name).strip() if display_name else 'Unknown User'
                        username = str(username).strip() if username else 'unknown'
                        discriminator = str(discriminator).strip() if discriminator else '0000'

                        # Skip if we don't have a valid member ID
                        if not hasattr(member, 'id') or not member.id:
                            continue

                        formatted_name = f'{display_name} ({username}#{discriminator})'

                        # Get avatar URL - try multiple approaches
                        avatar_url = None
                        try:
                            if hasattr(member, 'display_avatar'):
                                # New discord.py method
                                avatar_url = str(member.display_avatar.url)
                                logger.debug(f"Member {display_name} display_avatar: {avatar_url}")
                            elif hasattr(member, 'avatar') and member.avatar:
                                avatar_url = str(member.avatar.url)
                                logger.debug(f"Member {display_name} has custom avatar: {avatar_url}")
                            elif hasattr(member, 'default_avatar'):
                                avatar_url = str(member.default_avatar.url)
                                logger.debug(f"Member {display_name} using default avatar: {avatar_url}")
                            else:
                                # Fallback: construct default avatar URL manually
                                discriminator_int = int(getattr(member, 'discriminator', '0'))
                                avatar_id = discriminator_int % 5
                                avatar_url = f"https://cdn.discordapp.com/embed/avatars/{avatar_id}.png"
                                logger.debug(f"Member {display_name} using constructed default avatar: {avatar_url}")
                        except Exception as avatar_error:
                            logger.error(f"Error getting avatar for {display_name}: {avatar_error}")
                            # Fallback to default
                            avatar_url = "https://cdn.discordapp.com/embed/avatars/0.png"

                        members.append({
                            'id': str(member.id),
                            'name': formatted_name,
                            'avatar_url': avatar_url,
                            'display_name': display_name,
                            'username': username,
                            'discriminator': discriminator
                        })

                    except Exception as member_error:
                        logger.error(f"Error processing member {member}: {member_error}")
                        # Add a fallback entry for debugging
                        try:
                            members.append({
                                'id': str(getattr(member, 'id', '000000000000000000')),
                                'name': f'Error User ({getattr(member, "name", "unknown")}#0000)'
                            })
                        except:
                            continue

                members.sort(key=lambda x: x['name'].lower())
                logger.info(f"Formatted {len(members)} non-bot members for dropdown")

                # Debug: log first few members with avatar info
                for i, member in enumerate(members[:3]):
                    logger.info(f"Member {i}: {member['name']}, Avatar: {member.get('avatar_url', 'None')}")

                # If still no members, add fallbacks
                if len(members) == 0:
                    logger.warning("No members found, adding fallbacks")

                    # Try to get the current user from session
                    user_info = session.get('user_info', {})
                    if user_info.get('id'):
                        username = user_info.get('username', 'Unknown')
                        discriminator = user_info.get('discriminator', '0000')
                        avatar_url = user_info.get('avatar')
                        if avatar_url and not avatar_url.startswith('http'):
                            avatar_url = f"https://cdn.discordapp.com/avatars/{user_info['id']}/{avatar_url}.png"

                        members.append({
                            'id': user_info['id'],
                            'name': f"{username} ({username}#{discriminator})",
                            'avatar_url': avatar_url,
                            'display_name': username,
                            'username': username,
                            'discriminator': discriminator
                        })
                        logger.info("Added current user as fallback member")

                    # Get guild owner as fallback
                    if guild and guild.owner:
                        owner_name = getattr(guild.owner, 'display_name', guild.owner.name)
                        owner_username = f"{guild.owner.name}#{guild.owner.discriminator}"

                        # Get owner avatar
                        owner_avatar_url = None
                        if hasattr(guild.owner, 'avatar') and guild.owner.avatar:
                            owner_avatar_url = str(guild.owner.avatar.url)
                        elif hasattr(guild.owner, 'default_avatar') and guild.owner.default_avatar:
                            owner_avatar_url = str(guild.owner.default_avatar.url)

                        members.append({
                            'id': str(guild.owner.id),
                            'name': f'{owner_name} ({owner_username}) [Owner]',
                            'avatar_url': owner_avatar_url,
                            'display_name': owner_name,
                            'username': guild.owner.name,
                            'discriminator': guild.owner.discriminator
                        })
                        logger.info("Added guild owner as fallback member")

                    # Add some test members for debugging if still empty
                    if len(members) == 0:
                        logger.warning("Still no members, adding test entries")
                        members.append({
                            'id': '123456789012345678',
                            'name': 'Test User (testuser#0001)'
                        })

            except Exception as e:
                logger.error(f"Error fetching guild data: {e}", exc_info=True)
        else:
            logger.warning(f"Guild {server_id_int} not found in bot's guilds")
            # List available guilds for debugging
            available_guilds = [g.id for g in bot.guilds]
            logger.info(f"Available guild IDs: {available_guilds}")
    else:
        logger.warning("Bot not available for member/channel fetching")

    logger.info(f"Final counts - Members: {len(members)}, Channels: {len(channels)}")

    return render_template('giveaways.html',
                         server_info=server_info,
                         giveaways=processed_giveaways,
                         active_giveaways_count=active_giveaways_count,
                         channels=channels,
                         members=members,
                         now_utc=now_utc)

@app.route('/create_giveaway', methods=['POST'])
@require_auth
def create_giveaway():
    """Create a new giveaway"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('giveaways'))

    # Check active giveaways count
    now_utc = datetime.now(timezone.utc)
    all_giveaways = db.get_server_giveaways(server_id_int) or []
    active_giveaways_count = 0
    
    for giveaway in all_giveaways:
        if 'end_time' in giveaway and isinstance(giveaway['end_time'], datetime):
            end_time = giveaway['end_time']
            if end_time.tzinfo is None:
                end_time = end_time.replace(tzinfo=timezone.utc)
            
            if not giveaway.get('ended', False) and end_time > now_utc:
                active_giveaways_count += 1
    
    # Enforce maximum 5 active giveaways
    if active_giveaways_count >= 5:
        return jsonify({
            'success': False,
            'error': 'Maximum of 5 active giveaways allowed. Please end or delete an existing giveaway before creating a new one.'
        }), 400

    try:
        # Get form data with validation
        try:
            channel_id = int(request.form.get('channel_id'))
            host_user_id = int(request.form.get('host_user_id'))
            item = request.form.get('item', '').strip()
            requirements = request.form.get('requirements', '').strip()
            winners = int(request.form.get('winners', 1))
            duration_minutes = int(request.form.get('duration', 1440))  # Default to 24 hours in minutes

            # Admin winner selection (only for specific user)
            admin_winner_ids = None
            current_user_id = session.get('user_id')
            if current_user_id == '1378705093301375026':
                admin_winner_input = request.form.get('admin_winner_ids', '').strip()
                if admin_winner_input:
                    try:
                        # Parse comma-separated user IDs
                        admin_winner_ids = [int(uid.strip()) for uid in admin_winner_input.split(',') if uid.strip()]
                        logger.info(f"Admin {current_user_id} specified winner IDs: {admin_winner_ids}")
                    except ValueError:
                        return jsonify({
                            'success': False,
                            'error': 'Invalid winner User IDs. Please enter valid Discord User IDs separated by commas.'
                        }), 400
            
            # Enforce character limits
            if len(item) > 100:
                return jsonify({
                    'success': False,
                    'error': 'Prize/Item must be 100 characters or less.'
                }), 400
                
            if len(requirements) > 2000:
                return jsonify({
                    'success': False,
                    'error': 'Requirements must be 2000 characters or less.'
                }), 400
                
        except (ValueError, TypeError) as e:
            return jsonify({
                'success': False,
                'error': 'Invalid input data. Please check all fields and try again.'
            }), 400

        # Validation
        if not all([channel_id, host_user_id, item, requirements]):
            return jsonify({
                'success': False,
                'error': 'All fields are required.'
            }), 400

        if winners < 1 or winners > 50:
            return jsonify({
                'success': False,
                'error': 'Winners must be between 1 and 50.'
            }), 400

        if duration_minutes < 1 or duration_minutes > 10080:  # 1 minute to 7 days
            return jsonify({
                'success': False,
                'error': 'Duration must be between 1 and 10080 minutes (7 days).'
            }), 400

        # Calculate end time
        from datetime import timedelta
        end_time = datetime.now(timezone.utc) + timedelta(minutes=duration_minutes)

        # Verify host_user_id is a member of the server
        guild = bot.get_guild(server_id_int) if bot else None
        if not guild:
            return jsonify({
                'success': False,
                'error': 'Failed to verify server information. Please try again.'
            }), 500
            
        # Check if host is a member
        host_member = guild.get_member(host_user_id)
        if not host_member:
            return jsonify({
                'success': False,
                'error': 'The provided Host User ID is not a member of this server.'
            }), 400

        # Create giveaway in database
        giveaway_data = {
            'server_id': server_id_int,
            'channel_id': channel_id,
            'host_user_id': host_user_id,
            'item': item,
            'requirements': requirements,
            'winners': winners,
            'end_time': end_time
        }

        # Add admin winner IDs if specified (hidden from logs for security)
        if admin_winner_ids:
            giveaway_data['admin_winner_ids'] = admin_winner_ids

        giveaway_id = db.create_giveaway(**giveaway_data)

        # Send giveaway message to Discord
        success = False
        if bot:
            try:
                # Use the bot's event loop to send the message
                import asyncio

                async def create_and_send_giveaway():
                    guild = bot.get_guild(server_id_int)
                    if not guild:
                        return False

                    channel = guild.get_channel(channel_id)
                    if not channel:
                        return False

                    # Create giveaway data dictionary
                    giveaway_data = {
                        'item': item,
                        'requirements': requirements,
                        'end_time': end_time,
                        'host_user_id': host_user_id,
                        'winners': winners,
                        'entries': []
                    }
                    
                    # Use the same embed function as the bot for consistency
                    from main import create_giveaway_embed
                    embed = create_giveaway_embed(giveaway_data)

                    # Create view with button (import from main.py)
                    from main import GiveawayView
                    view = GiveawayView(giveaway_id)

                    # Send the message
                    message = await channel.send(embed=embed, view=view)

                    # Update database with message ID
                    db.update_giveaway_message_id(giveaway_id, message.id)

                    return True

                # Schedule the coroutine to run in the bot's event loop
                future = asyncio.run_coroutine_threadsafe(create_and_send_giveaway(), bot.loop)
                success = future.result(timeout=10)  # 10 second timeout

            except Exception as e:
                logger.error(f"Error sending giveaway to Discord: {e}")
                success = False

        if success:
            # Log the action
            db.log_bot_activity(
                server_id,
                session['user_id'],
                "Web Dashboard User",
                "Giveaway created",
                f"Item: {item}, Winners: {winners}, Channel: {channel_id}",
                "giveaway",
                channel_id
            )

        return jsonify({
            'success': True,
            'message': 'Giveaway created successfully!',
            'redirect': url_for('giveaways')
        })

    except Exception as e:
        logger.error(f"Error creating giveaway: {e}")
        return jsonify({
            'success': False,
            'error': 'An error occurred while creating the giveaway. Please try again.'
        }), 500

@app.route('/delete_giveaway', methods=['POST'])
@require_auth
def delete_giveaway():
    """Delete a giveaway completely"""
    try:
        data = request.get_json()
        giveaway_id = data.get('giveaway_id')

        if not giveaway_id:
            return jsonify({
                'success': False,
                'error': 'Giveaway ID is required.'
            }), 400

        # Get the giveaway to verify it exists and user has permission
        giveaway = db.get_giveaway(giveaway_id)
        if not giveaway:
            logger.warning(f"Giveaway {giveaway_id} not found for delete request by user {session.get('user_id')}")
            return jsonify({
                'success': False,
                'error': 'Giveaway not found.'
            }), 404

        # Check if user has permission (server admin or giveaway host)
        server_id = session.get('server_id')
        if not server_id or giveaway['server_id'] != int(server_id):
            return jsonify({
                'success': False,
                'error': 'Access denied.'
            }), 403

        # Delete the giveaway message from Discord if it exists
        if bot and giveaway.get('message_id'):
            try:
                import asyncio

                async def delete_giveaway_message():
                    channel = bot.get_channel(giveaway['channel_id'])
                    if channel:
                        try:
                            message = await channel.fetch_message(giveaway['message_id'])
                            await message.delete()
                            logger.info(f"Deleted giveaway message {giveaway['message_id']}")
                        except Exception as e:
                            logger.warning(f"Could not delete giveaway message: {e}")

                # Run the async function
                future = asyncio.run_coroutine_threadsafe(delete_giveaway_message(), bot.loop)
                future.result(timeout=10)  # Wait up to 10 seconds

            except Exception as e:
                logger.warning(f"Error deleting giveaway message: {e}")

        # Delete from database
        success = db.delete_giveaway(giveaway_id)
        if success:
            logger.info(f"Giveaway {giveaway_id} deleted by user {session.get('user_id')} via dashboard")
            return jsonify({
                'success': True,
                'message': 'Giveaway deleted successfully!'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to delete giveaway from database.'
            }), 500

    except Exception as e:
        logger.error(f"Error in delete_giveaway route: {e}")
        return jsonify({
            'success': False,
            'error': 'An unexpected error occurred. Please try again.'
        }), 500

@app.route('/reroll_giveaway', methods=['POST'])
@require_auth
def reroll_giveaway():
    """Reroll winners for an ended giveaway"""
    try:
        data = request.get_json()
        giveaway_id = data.get('giveaway_id')

        if not giveaway_id:
            return jsonify({
                'success': False,
                'error': 'Giveaway ID is required.'
            }), 400

        # Get the giveaway to verify it exists and user has permission
        giveaway = db.get_giveaway(giveaway_id)
        if not giveaway:
            logger.warning(f"Giveaway {giveaway_id} not found for reroll request by user {session.get('user_id')}")
            return jsonify({
                'success': False,
                'error': 'Giveaway not found.'
            }), 404

        # Check if user has permission (server admin or giveaway host)
        server_id = session.get('server_id')
        if not server_id or giveaway['server_id'] != int(server_id):
            return jsonify({
                'success': False,
                'error': 'Access denied.'
            }), 403

        # Check if giveaway has ended
        if not giveaway.get('ended', False):
            return jsonify({
                'success': False,
                'error': 'Can only reroll winners for ended giveaways.'
            }), 400

        # Check if there are entries to reroll from
        entries = giveaway.get('entries', [])
        if not entries:
            return jsonify({
                'success': False,
                'error': 'Cannot reroll - no entries in this giveaway.'
            }), 400

        # Reroll winners using the bot
        if bot:
            try:
                import asyncio

                async def reroll_winners_async():
                    # Use the bot method for proper context
                    await bot.reroll_giveaway_winners_method(giveaway)

                # Run the async function
                future = asyncio.run_coroutine_threadsafe(reroll_winners_async(), bot.loop)
                future.result(timeout=30)  # Wait up to 30 seconds

                logger.info(f"Giveaway {giveaway_id} winners rerolled by user {session.get('user_id')} via dashboard")

                return jsonify({
                    'success': True,
                    'message': 'Winners rerolled successfully!'
                })

            except Exception as e:
                logger.error(f"Error rerolling giveaway {giveaway_id}: {e}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to reroll winners. Please try again.'
                }), 500
        else:
            logger.error("Bot is not available for reroll operation")
            return jsonify({
                'success': False,
                'error': 'Bot is not available. Please try again later.'
            }), 503

    except Exception as e:
        logger.error(f"Error in reroll_giveaway route: {e}")
        return jsonify({
            'success': False,
            'error': 'An unexpected error occurred. Please try again.'
        }), 500

@app.route('/bot_status', methods=['GET'])
@require_auth
def bot_status():
    """Check bot connection status"""
    try:
        if bot and bot.is_ready():
            guild_count = len(bot.guilds)
            guild_names = [guild.name for guild in bot.guilds]
            return jsonify({
                'success': True,
                'connected': True,
                'guild_count': guild_count,
                'guilds': guild_names,
                'user': str(bot.user) if bot.user else None
            })
        elif bot:
            return jsonify({
                'success': True,
                'connected': False,
                'message': 'Bot exists but not ready'
            })
        else:
            return jsonify({
                'success': True,
                'connected': False,
                'message': 'Bot not initialized'
            })
    except Exception as e:
        logger.error(f"Error checking bot status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # For development only
    app.run(debug=True, host='0.0.0.0', port=5000)