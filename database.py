import pymongo
import asyncio
import logging
from typing import Optional, List, Dict, Any, Tuple
from pymongo import MongoClient
from datetime import datetime, timedelta, timezone
from bson import ObjectId
try:
    import discord
except ImportError:
    discord = None

logger = logging.getLogger(__name__)

def ensure_timezone_aware(dt):
    """Ensure a datetime object is timezone-aware (UTC if naive)"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt

class DatabaseManager:
    def __init__(self, mongo_url: str):
        self.mongo_url = mongo_url
        self.client = None
        self.db = None
        self._ensure_connected()
        
    # Server Configuration Methods
    def get_server_config(self, server_id: int) -> dict:
        """
        Get server configuration

        Args:
            server_id: Discord server ID

        Returns:
            dict: Server configuration or empty dict if not found
        """
        self._ensure_connected()
        collection = self.db['leakin-server-configs']
        # Try both string and integer server_id for backward compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result or {}
        
    def update_server_config(self, server_id: int, updates: dict) -> bool:
        """
        Update server configuration
        
        Args:
            server_id: Discord server ID
            updates: Dictionary of fields to update
            
        Returns:
            bool: True if update was successful
        """
        self._ensure_connected()
        collection = self.db['leakin-server-configs']
        
        # Add timestamp for the update
        updates['updated_at'] = datetime.now(timezone.utc)

        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": updates, "$setOnInsert": {"created_at": datetime.now(timezone.utc)}},
            upsert=True
        )
        
        return result.modified_count > 0 or result.upserted_id is not None

    def update_server_config_field(self, server_id: int, field: str, value: any) -> bool:
        """
        Update a single field in server configuration

        Args:
            server_id: Discord server ID
            field: Field name to update
            value: New value for the field

        Returns:
            bool: True if successful, False otherwise
        """
        return self.update_server_config(server_id, {field: value})

    def is_system_enabled(self, server_id: int, system_name: str) -> bool:
        """
        Check if a specific system is enabled for a server

        Args:
            server_id: Discord server ID
            system_name: Name of the system (repping, vent, tempvoice, etc.)

        Returns:
            bool: True if system is enabled, False otherwise
        """
        try:
            config = self.get_server_config(server_id)
            if not config:
                return False

            # Check if the system is explicitly disabled
            enabled_field = f"{system_name}_enabled"
            if enabled_field in config:
                return config[enabled_field]

            # For backward compatibility, check if system has required configuration
            if system_name == "repping":
                return bool(config.get("repping_role_id") and config.get("repping_trigger_word"))
            elif system_name == "vent":
                return bool(config.get("vent_channel_id"))
            elif system_name == "tempvoice":
                # Check tempvoice settings in separate collection
                tempvoice_settings = self.get_tempvoice_settings(server_id)
                if not tempvoice_settings:
                    return False
                # Check if explicitly disabled
                if "enabled" in tempvoice_settings and not tempvoice_settings["enabled"]:
                    return False
                # Check if properly configured
                return bool(tempvoice_settings.get("interface_channel_id") and
                           tempvoice_settings.get("creator_channel_id"))
            elif system_name == "sticky_messages":
                return bool(config.get("sticky_messages"))
            elif system_name == "dm_support":
                return bool(config.get("dm_support_category_id"))
            elif system_name == "gender_verification":
                return bool(config.get("gender_verification_channel_id"))

            return False
        except Exception as e:
            logger.error(f"Error checking if {system_name} is enabled for server {server_id}: {e}")
            return False

    def _ensure_connected(self):
        """Ensure we have an active database connection"""
        if self.client is None:
            self.connect()
            
    def connect(self):
        """Connect to MongoDB"""
        try:
            if self.client is not None:
                try:
                    # Test existing connection
                    self.client.admin.command('ping')
                    return  # Already connected
                except:
                    # Connection failed, will create a new one
                    self.disconnect()
                    
            self.client = MongoClient(self.mongo_url)
            self.db = self.client['leakin']  # Use the leakin database
            # Test connection
            self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self.client = None
            self.db = None
            raise e
    
    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    # License Key Operations
    def add_license_keys(self, keys: List[str]) -> int:
        """Add multiple license keys to the database"""
        collection = self.db['leakin-license-keys']
        
        key_docs = []
        for key in keys:
            # Check if key already exists
            if not collection.find_one({"key": key}):
                key_docs.append({
                    "key": key,
                    "redeemed": False,
                    "redeemed_by": None,
                    "redeemed_at": None,
                    "server_id": None,
                    "created_at": datetime.now(timezone.utc)
                })
        
        if key_docs:
            result = collection.insert_many(key_docs)
            logger.info(f"Added {len(result.inserted_ids)} new license keys")
            return len(result.inserted_ids)
        return 0
    
    def redeem_license_key(self, key: str, user_id: int, server_id: int) -> bool:
        """Redeem a license key for a user and server"""
        collection = self.db['leakin-license-keys']
        
        # Find unredeemed key
        key_doc = collection.find_one({"key": key, "redeemed": False})
        if not key_doc:
            return False
        
        # Update key as redeemed
        result = collection.update_one(
            {"_id": key_doc["_id"]},
            {
                "$set": {
                    "redeemed": True,
                    "redeemed_by": user_id,
                    "redeemed_at": datetime.utcnow(),
                    "server_id": server_id
                }
            }
        )
        
        return result.modified_count > 0
    
    def get_user_license_keys(self, user_id: int, include_disabled: bool = False) -> List[Dict[str, Any]]:
        """Get all license keys owned by a user
        
        Args:
            user_id: The Discord user ID to get license keys for
            include_disabled: Whether to include disabled license keys (default: False)
            
        Returns:
            List of license key documents with server info and disabled status
        """
        try:
            # Ensure user_id is an integer for consistent querying
            user_id = int(user_id)
            logger.info(f"Querying license keys for user_id: {user_id} (type: {type(user_id)})")
            
            collection = self.db['leakin-license-keys']
            
            # First, try with integer user_id
            query = {"redeemed_by": user_id, "redeemed": True}
            
            if not include_disabled:
                query["$or"] = [
                    {"disabled": {"$exists": False}},
                    {"disabled": False}
                ]
            
            # Log the query for debugging
            logger.info(f"Running query: {query}")
            
            # Execute the query
            results = list(collection.find(query))
            logger.info(f"Found {len(results)} license keys for user {user_id}")
            
            # Log first few results for debugging
            if results:
                logger.info(f"Sample license key: {results[0]}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in get_user_license_keys: {str(e)}", exc_info=True)
            return []
    
    def get_server_license_key(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the active license key for a server (only if not disabled)"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({
            "server_id": server_id,
            "redeemed": True,
            "$or": [
                {"disabled": {"$exists": False}},
                {"disabled": False}
            ]
        })
    
    def transfer_key_to_server(self, key: str, user_id: int, new_server_id: int) -> bool:
        """Transfer a license key to a different server with cleanup of old server's data"""
        collection = self.db['leakin-license-keys']
        
        # First get the current server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": user_id})
        if not current_key:
            return False
            
        old_server_id = current_key.get('server_id')
        
        # Update the server_id
        result = collection.update_one(
            {"key": key, "redeemed_by": user_id},
            {"$set": {"server_id": new_server_id}}
        )
        
        # If transfer was successful, clean up old server's role assignments
        if result.modified_count > 0 and old_server_id:
            # Clean up server config for old server
            config_collection = self.db['leakin-server-configs']
            config_collection.delete_one({"server_id": old_server_id})
            logger.info(f"Transferred key {key} from server {old_server_id} to {new_server_id}, cleaned up old config")
            
        return result.modified_count > 0
    
    def transfer_key_to_user(self, key: str, current_user_id: int, new_user_id: int) -> bool:
        """Transfer ownership of a license key to another user with cleanup"""
        collection = self.db['leakin-license-keys']
        
        # First get the server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": current_user_id})
        if not current_key:
            return False
            
        server_id = current_key.get('server_id')
        
        # Update the owner
        result = collection.update_one(
            {"key": key, "redeemed_by": current_user_id},
            {"$set": {"redeemed_by": new_user_id}}
        )
        
        # If transfer was successful, clean up server configuration
        if result.modified_count > 0 and server_id:
            # Clean up server config since the new owner will need to reconfigure
            config_collection = self.db['leakin-server-configs']
            config_collection.delete_one({"server_id": server_id})
            logger.info(f"Transferred key {key} from user {current_user_id} to {new_user_id} for server {server_id}, cleaned up server config")
            
        return result.modified_count > 0
    
    # Server Configuration Operations
    def save_server_config(self, server_id: int, config: Dict[str, Any]) -> bool:
        """Save server configuration"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {**config, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def get_server_config_alt(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server configuration (alternative method - deprecated, use get_server_config instead)"""
        collection = self.db['leakin-server-configs']
        # Try both string and integer server_id for backward compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result
    
    def update_server_config_field(self, server_id: int, field: str, value: Any) -> bool:
        """Update a specific field in server configuration"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {field: value, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def add_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Add a user to the ignored list for a server"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$addToSet": {"ignored_users": user_id}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def remove_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Remove a user from the ignored list for a server"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$pull": {"ignored_users": user_id}}
        )
        
        return result.modified_count > 0
    
    def is_server_licensed(self, server_id: int) -> bool:
        """Check if a server has a valid license"""
        license_key = self.get_server_license_key(server_id)
        return license_key is not None and not license_key.get('disabled', False)

    def disable_license_key(self, key: str) -> bool:
        """Disable a license key and all its features"""
        collection = self.db['leakin-license-keys']

        result = collection.update_one(
            {"key": key},
            {"$set": {"disabled": True, "disabled_at": datetime.utcnow()}}
        )

        return result.modified_count > 0

    def enable_license_key(self, key: str) -> bool:
        """Enable a license key"""
        collection = self.db['leakin-license-keys']

        result = collection.update_one(
            {"key": key},
            {"$set": {"disabled": False}, "$unset": {"disabled_at": ""}}
        )

        return result.modified_count > 0

    def get_license_key_by_key(self, key: str) -> Optional[Dict[str, Any]]:
        """Get license key information by key string"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({"key": key})

    def get_server_license_key_full(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the full license key information for a server"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({"server_id": server_id, "redeemed": True})
    
    def is_server_configured(self, server_id: int) -> tuple[bool, List[str]]:
        """Check if server is properly configured"""
        config = self.get_server_config(server_id)
        if not config:
            return False, ["No configuration found"]
        
        required_fields = ['role_id', 'channel_id', 'trigger_word']
        missing_fields = []
        
        for field in required_fields:
            if field not in config or config[field] is None:
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields
        
    # Gender Verification Methods
    def set_gender_verification_settings(self, server_id: int, channel_id: int, category_id: int, 
                                       support_role_id: int, paper_text: str) -> bool:
        """Save gender verification settings for a server"""
        collection = self.db['leakin-gender-verification']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "channel_id": channel_id,
                "category_id": category_id,
                "support_role_id": support_role_id,
                "paper_text": paper_text,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def get_gender_verification_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get gender verification settings for a server"""
        collection = self.db['leakin-gender-verification']
        return collection.find_one({"server_id": server_id})

    def update_gender_verification_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update gender verification system enabled status"""
        collection = self.db['leakin-gender-verification']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0
    
    def create_gender_verification_ticket(self, server_id: int, user_id: int) -> str:
        """Create a new gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        
        # Check if user already has an open ticket
        existing_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
        
        if existing_ticket:
            return "existing"
            
        # Check if user had a ticket closed in the last 12 hours
        recent_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "closed",
            "closed_at": {"$gt": datetime.now(timezone.utc) - timedelta(hours=12)}
        })
        
        if recent_ticket:
            return "recent"
            
        # Create new ticket
        ticket = {
            "server_id": server_id,
            "user_id": user_id,
            "channel_id": None,
            "status": "open",
            "created_at": datetime.utcnow(),
            "closed_at": None
        }
        
        result = collection.insert_one(ticket)
        return str(result.inserted_id)
    
    def close_gender_verification_ticket(self, ticket_id: str, channel_id: int) -> bool:
        """Close a gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        
        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.utcnow(),
                "channel_id": channel_id
            }}
        )
        
        return result.modified_count > 0
    
    def get_user_open_ticket(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        return collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
    
    def cleanup_old_tickets(self):
        """Clean up tickets that have been inactive for more than 24 hours"""
        try:
            # Check if database is initialized by trying to access a collection
            try:
                collection = self.db.get_collection('leakin-gender-tickets')
            except Exception as e:
                logger.error(f"Database error: {e}")
                return 0

            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            # Find all open tickets
            open_tickets = collection.find({"status": "open"})
            closed_count = 0
            
            for ticket in open_tickets:
                last_activity = ticket.get('created_at')

                # If there are messages, use the timestamp of the last message
                if 'messages' in ticket and ticket['messages']:
                    last_message = max(ticket['messages'], key=lambda x: x['timestamp'])
                    last_activity = last_message['timestamp']

                # Ensure last_activity is timezone-aware for comparison
                if last_activity and last_activity.tzinfo is None:
                    # If timezone-naive, assume it's UTC
                    last_activity = last_activity.replace(tzinfo=timezone.utc)

                # If last activity was more than 24 hours ago, close the ticket
                if last_activity and last_activity < cutoff_time:
                    result = collection.update_one(
                        {"_id": ticket['_id']},
                        {"$set": {"status": "closed", "closed_at": datetime.now(timezone.utc)}}
                    )
                    
                    if result.modified_count > 0:
                        closed_count += 1
                        logger.info(f"Closed inactive ticket {ticket['_id']} (last activity: {last_activity})")
            
            if closed_count > 0:
                logger.info(f"Cleaned up {closed_count} inactive tickets")
            else:
                logger.debug("No inactive tickets to clean up")
                
            return closed_count

        except Exception as e:
            logger.error(f"Error in cleanup_old_tickets: {e}", exc_info=True)
            return 0

    # TempVoice Methods
    def set_tempvoice_settings(self, server_id: int, interface_channel_id: int, creator_channel_id: int, default_user_limit: Optional[int] = None) -> bool:
        """Save TempVoice settings for a server"""
        collection = self.db['leakin-tempvoice-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "interface_channel_id": interface_channel_id,
                "creator_channel_id": creator_channel_id,
                "default_user_limit": default_user_limit,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_tempvoice_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get TempVoice settings for a server"""
        collection = self.db['leakin-tempvoice-settings']
        return collection.find_one({"server_id": server_id})

    def update_tempvoice_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update TempVoice system enabled status"""
        collection = self.db['leakin-tempvoice-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0

    def create_temp_channel(self, server_id: int, user_id: int, channel_id: int) -> bool:
        """Create a temporary voice channel record"""
        collection = self.db['leakin-temp-channels']

        # Check if user already has a channel
        existing = collection.find_one({
            "server_id": server_id,
            "owner_id": user_id,
            "active": True
        })

        if existing:
            return False

        channel_doc = {
            "server_id": server_id,
            "channel_id": channel_id,
            "owner_id": user_id,
            "active": True,
            "user_limit": None,
            "blocked_users": [],
            "locked": False,
            "created_at": datetime.utcnow()
        }

        result = collection.insert_one(channel_doc)
        return result.inserted_id is not None

    def get_temp_channel(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get temporary channel data"""
        collection = self.db['leakin-temp-channels']
        return collection.find_one({"channel_id": channel_id, "active": True})

    def get_user_temp_channel(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's active temporary channel"""
        collection = self.db['leakin-temp-channels']
        return collection.find_one({
            "server_id": server_id,
            "owner_id": user_id,
            "active": True
        })

    def delete_temp_channel(self, channel_id: int) -> bool:
        """Mark temporary channel as inactive"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id},
            {"$set": {"active": False, "deleted_at": datetime.utcnow()}}
        )

        return result.modified_count > 0

    def update_temp_channel_owner(self, channel_id: int, new_owner_id: int) -> bool:
        """Transfer ownership of a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"owner_id": new_owner_id}}
        )

        return result.modified_count > 0

    def set_temp_channel_limit(self, channel_id: int, user_limit: Optional[int]) -> bool:
        """Set user limit for temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"user_limit": user_limit}}
        )

        return result.modified_count > 0

    def block_user_from_temp_channel(self, channel_id: int, user_id: int) -> bool:
        """Block a user from a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$addToSet": {"blocked_users": user_id}}
        )

        return result.modified_count > 0

    def unblock_user_from_temp_channel(self, channel_id: int, user_id: int) -> bool:
        """Unblock a user from a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$pull": {"blocked_users": user_id}}
        )

        return result.modified_count > 0

    def set_temp_channel_lock(self, channel_id: int, locked: bool) -> bool:
        """Lock or unlock a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"locked": locked}}
        )

        return result.modified_count > 0

    def get_all_active_temp_channels(self) -> List[Dict[str, Any]]:
        """Get all active temporary channels across all servers"""
        collection = self.db['leakin-temp-channels']
        return list(collection.find({"active": True}))

    # Vent System Methods
    def set_vent_settings(self, server_id: int, vent_channel_id: int) -> bool:
        """Save vent system settings for a server"""
        collection = self.db['leakin-vent-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "vent_channel_id": vent_channel_id,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_vent_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get vent system settings for a server"""
        collection = self.db['leakin-vent-settings']
        return collection.find_one({"server_id": server_id})

    def update_vent_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update vent system enabled status"""
        collection = self.db['leakin-vent-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0

    def log_vent_message(self, server_id: int, user_id: int, username: str, message: str) -> bool:
        """Log a vent message for moderation purposes"""
        collection = self.db['leakin-vent-logs']

        log_doc = {
            "server_id": server_id,
            "user_id": user_id,
            "username": username,
            "message": message,
            "timestamp": datetime.utcnow()
        }

        result = collection.insert_one(log_doc)
        return result.inserted_id is not None

    # Sticky Message Methods
    def set_sticky_message(self, server_id: int, channel_id: int, content: str, created_by: int) -> bool:
        """Set or update a sticky message for a channel"""
        collection = self.db['leakin-sticky-messages']

        result = collection.update_one(
            {"server_id": server_id, "channel_id": channel_id},
            {"$set": {
                "content": content,
                "created_by": created_by,
                "message_id": None,  # Will be set when message is posted
                "updated_at": datetime.now(timezone.utc)
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_sticky_message(self, server_id: int, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get sticky message settings for a specific channel"""
        collection = self.db['leakin-sticky-messages']
        return collection.find_one({"server_id": server_id, "channel_id": channel_id})

    def get_all_sticky_messages(self, server_id: int) -> List[Dict[str, Any]]:
        """Get all sticky messages for a server"""
        collection = self.db['leakin-sticky-messages']
        return list(collection.find({"server_id": server_id}))

    def update_sticky_messages_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update all sticky messages enabled status for a server"""
        collection = self.db['leakin-sticky-messages']

        result = collection.update_many(
            {"server_id": server_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0

    def update_sticky_message_id(self, server_id: int, channel_id: int, message_id: int) -> bool:
        """Update the message ID for a sticky message"""
        collection = self.db['leakin-sticky-messages']

        result = collection.update_one(
            {"server_id": server_id, "channel_id": channel_id},
            {"$set": {"message_id": message_id}}
        )

        return result.modified_count > 0

    def create_sticky_message(self, server_id: int, channel_id: int, content: str) -> bool:
        """Alias for set_sticky_message for web dashboard compatibility"""
        return self.set_sticky_message(server_id, channel_id, content, 0)  # Use 0 for web dashboard user

    def remove_sticky_message(self, server_id: int, channel_id: int) -> bool:
        """Remove a sticky message from a channel"""
        collection = self.db['leakin-sticky-messages']

        result = collection.delete_one({"server_id": server_id, "channel_id": channel_id})
        return result.deleted_count > 0

    def log_sticky_activity(self, server_id: int, channel_id: int, user_id: int, username: str, action: str, content: str = None) -> bool:
        """Log sticky message activity for moderation purposes"""
        collection = self.db['leakin-sticky-logs']

        log_doc = {
            "server_id": server_id,
            "channel_id": channel_id,
            "user_id": user_id,
            "username": username,
            "action": action,  # 'created', 'removed', 'reposted'
            "content": content,
            "timestamp": datetime.now(timezone.utc)
        }

        result = collection.insert_one(log_doc)
        return result.inserted_id is not None

    # Comprehensive Logging System
    def log_bot_activity(self, server_id: int, user_id: int, username: str, action: str,
                        details: str = None, category: str = "general", channel_id: int = None) -> bool:
        """Log all bot activity for dashboard viewing (max 100 logs per server)"""
        collection = self.db['leakin-bot-logs']

        log_doc = {
            "server_id": server_id,
            "user_id": user_id,
            "username": username,
            "action": action,
            "details": details,
            "category": category,  # 'repping', 'vent', 'tempvoice', 'sticky', 'dm_support', 'gender_verification', 'config', 'general'
            "channel_id": channel_id,
            "timestamp": datetime.now(timezone.utc)
        }

        # Insert the new log
        result = collection.insert_one(log_doc)

        if result.inserted_id:
            # Clean up old logs - keep only the most recent 100 logs per server
            self._cleanup_old_logs(server_id)
            return True

        return False

    def _cleanup_old_logs(self, server_id: int, max_logs: int = 100):
        """Clean up old logs, keeping only the most recent max_logs entries per server"""
        collection = self.db['leakin-bot-logs']

        try:
            # Count current logs for this server
            total_logs = collection.count_documents({"server_id": server_id})

            if total_logs > max_logs:
                # Find the oldest logs to delete
                logs_to_delete = total_logs - max_logs

                # Get the oldest logs
                oldest_logs = list(collection.find(
                    {"server_id": server_id}
                ).sort("timestamp", 1).limit(logs_to_delete))

                if oldest_logs:
                    # Delete the oldest logs
                    oldest_ids = [log["_id"] for log in oldest_logs]
                    collection.delete_many({"_id": {"$in": oldest_ids}})

                    logger.info(f"Cleaned up {len(oldest_ids)} old log entries for server {server_id}")

        except Exception as e:
            logger.error(f"Error cleaning up old logs for server {server_id}: {e}")

    def get_bot_logs(self, server_id: int, limit: int = 100, category: str = None,
                    start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get bot activity logs for dashboard"""
        collection = self.db['leakin-bot-logs']

        query = {"server_id": server_id}

        if category:
            query["category"] = category

        if start_date or end_date:
            date_query = {}
            if start_date:
                date_query["$gte"] = start_date
            if end_date:
                date_query["$lte"] = end_date
            query["timestamp"] = date_query

        return list(collection.find(query).sort("timestamp", -1).limit(limit))

    def get_log_statistics(self, server_id: int, days: int = 7) -> Dict[str, Any]:
        """Get comprehensive log statistics for dashboard with multiple datasets"""
        collection = self.db['leakin-bot-logs']
        now = datetime.now(timezone.utc)

        # Handle "all time" stats (when days is 0 or very large)
        if days == 0 or days > 36500:  # 0 or more than 100 years = all time
            start_date = datetime(2020, 1, 1, tzinfo=timezone.utc)  # Bot creation era
            period_description = "all time"
            total_logs = collection.count_documents({"server_id": server_id})
        else:
            # For specific time period, filter by date
            start_date = now - timedelta(days=days)
            period_description = f"last {days} days"
            total_logs = collection.count_documents({
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            })

        # Get activity counts by category
        category_pipeline = [
            {"$match": {
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            }},
            {"$group": {
                "_id": "$category",
                "count": {"$sum": 1}
            }},
            {"$sort": {"count": -1}}
        ]

        category_stats = {}
        for result in collection.aggregate(category_pipeline):
            if result['_id']:  # Skip null categories
                category_stats[result['_id']] = result['count']

        # Get daily activity for chart (overall)
        daily_pipeline = [
            {"$match": {
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            }},
            {"$group": {
                "_id": {
                    "$dateToString": {
                        "format": "%Y-%m-%d",
                        "date": "$timestamp"
                    }
                },
                "count": {"$sum": 1}
            }},
            {"$sort": {"_id": 1}}
        ]

        daily_activity = {}
        for result in collection.aggregate(daily_pipeline):
            daily_activity[result['_id']] = result['count']

        # Get specific activity metrics by date
        specific_metrics_pipeline = [
            {"$match": {
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            }},
            {"$group": {
                "_id": {
                    "date": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$timestamp"
                        }
                    },
                    "metric_type": {
                        "$switch": {
                            "branches": [
                                {
                                    "case": {"$and": [
                                        {"$eq": ["$category", "repping"]},
                                        {"$regexMatch": {"input": "$action", "regex": "Role assigned"}}
                                    ]},
                                    "then": "users_repping"
                                },
                                {
                                    "case": {"$eq": ["$category", "tempvoice"]},
                                    "then": "voice_channels_created"
                                },
                                {
                                    "case": {"$or": [
                                        {"$eq": ["$category", "message_sent"]},
                                        {"$regexMatch": {"input": "$action", "regex": "^Message sent$"}},
                                        {"$regexMatch": {"input": "$action", "regex": "^Sent message$"}}
                                    ]},
                                    "then": "messages_sent"
                                },
                                {
                                    "case": {"$or": [
                                        {"$eq": ["$category", "message_deleted"]},
                                        {"$regexMatch": {"input": "$action", "regex": "^Message deleted$"}},
                                        {"$regexMatch": {"input": "$action", "regex": "^Deleted message$"}}
                                    ]},
                                    "then": "messages_deleted"
                                }
                            ],
                            "default": "other"
                        }
                    }
                },
                "count": {"$sum": 1}
            }},
            {"$match": {"_id.metric_type": {"$ne": "other"}}},
            {"$sort": {"_id.date": 1}}
        ]

        daily_by_category = {}
        for result in collection.aggregate(specific_metrics_pipeline):
            date = result['_id']['date']
            metric_type = result['_id']['metric_type']
            if date not in daily_by_category:
                daily_by_category[date] = {}
            daily_by_category[date][metric_type] = result['count']

        return {
            "total_logs": total_logs,
            "daily_activity": daily_activity,
            "daily_by_category": daily_by_category,
            "category_stats": category_stats,
            "period_days": days if days > 0 and days <= 36500 else 0,
            "period_description": period_description
        }

    def get_server_statistics(self, server_id: int, bot=None) -> Dict[str, Any]:
        """Get comprehensive server statistics for dashboard"""
        stats = {}

        # Initialize collections that will be used throughout the method
        logs_collection = self.db['leakin-bot-logs']

        # Discord server statistics (live from Discord API if bot is available)
        if bot:
            guild = bot.get_guild(server_id)
            if guild:
                # Basic server info
                stats['member_count'] = guild.member_count
                stats['channel_count'] = len(guild.channels)
                stats['premium_subscription_count'] = guild.premium_subscription_count or 0

                # Online member count (requires presence intent)
                try:
                    if discord:
                        online_count = len([member for member in guild.members if member.status != discord.Status.offline])
                        stats['online_count'] = online_count
                    else:
                        stats['online_count'] = 0
                except:
                    # Fallback if presence intent is not available
                    stats['online_count'] = 0

                # Server creation date
                stats['server_created'] = guild.created_at
                stats['server_name'] = guild.name
                stats['server_icon'] = str(guild.icon.url) if guild.icon else None
            else:
                # Guild not found or bot not in guild
                stats['member_count'] = 0
                stats['channel_count'] = 0
                stats['premium_subscription_count'] = 0
                stats['online_count'] = 0
                stats['server_created'] = None
                stats['server_name'] = 'Unknown Server'
                stats['server_icon'] = None
        else:
            # Bot not available - use placeholder values
            stats['member_count'] = 0
            stats['channel_count'] = 0
            stats['premium_subscription_count'] = 0
            stats['online_count'] = 0
            stats['server_created'] = None
            stats['server_name'] = 'Unknown Server'
            stats['server_icon'] = None

        # Repping statistics (live from Discord if bot is available)
        if bot:
            config = self.get_server_config(server_id)
            if config and config.get('role_id'):
                guild = bot.get_guild(server_id)
                if guild:
                    role = guild.get_role(config['role_id'])
                    if role:
                        # Count non-bot members who have the role
                        stats['repping_users'] = len([member for member in role.members if not member.bot])
                    else:
                        stats['repping_users'] = 0
                else:
                    stats['repping_users'] = 0
            else:
                stats['repping_users'] = 0
        else:
            # Fallback to log-based method
            config_collection = self.db['leakin-server-config']
            config = config_collection.find_one({"server_id": server_id})

            if config and config.get('role_id'):
                # Count users who currently have repping role assignments (live count)
                # Get users who have had role assignments more recently than removals
                pipeline = [
                    {"$match": {
                        "server_id": server_id,
                        "category": "repping",
                        "$or": [
                            {"action": {"$regex": "^Role assigned:"}},
                            {"action": {"$regex": "^Role removed:"}}
                        ]
                    }},
                    {"$sort": {"timestamp": -1}},
                    {"$group": {
                        "_id": "$user_id",
                        "latest_action": {"$first": "$action"}
                    }},
                    {"$match": {"latest_action": {"$regex": "^Role assigned:"}}},
                    {"$count": "active_reppers"}
                ]

                result = list(logs_collection.aggregate(pipeline))
                stats['repping_users'] = result[0]['active_reppers'] if result else 0
            else:
                stats['repping_users'] = 0

        # Temp voice statistics
        temp_channels_collection = self.db['leakin-temp-channels']
        active_temp_channels = temp_channels_collection.count_documents({
            "server_id": server_id,
            "active": True
        })
        stats['active_temp_channels'] = active_temp_channels

        # Total temp channels created (last 30 days)
        total_temp_channels = temp_channels_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['total_temp_channels_30d'] = total_temp_channels

        # Sticky messages (live count)
        sticky_collection = self.db['leakin-sticky-messages']
        active_sticky_messages = sticky_collection.count_documents({
            "server_id": server_id
        })
        stats['active_sticky_messages'] = active_sticky_messages

        # Gender verification tickets (last 30 days)
        gender_tickets_collection = self.db['leakin-gender-tickets']
        gender_tickets = gender_tickets_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['gender_tickets_30d'] = gender_tickets

        # DM Support tickets (last 30 days)
        dm_support_collection = self.db['leakin-dm-support-tickets']
        dm_support_tickets = dm_support_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['dm_support_tickets_30d'] = dm_support_tickets

        # Bot activity (live/all time)
        total_activity = logs_collection.count_documents({
            "server_id": server_id
        })
        stats['total_bot_activity'] = total_activity
        stats['total_actions'] = total_activity  # Alias for template compatibility

        # Active users (users who have interacted with bot in last 30 days)
        active_users_count = len(logs_collection.distinct("user_id", {
            "server_id": server_id,
            "timestamp": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        }))
        stats['active_users'] = active_users_count

        return stats

    def get_top_repping_users(self, server_id: int, limit: int = 6, bot=None) -> List[Dict[str, Any]]:
        """Get top repping users by total hours spent repping"""
        try:
            logs_collection = self.db['leakin-bot-logs']

            # Get all role assignment and removal events for repping
            repping_events = list(logs_collection.find({
                "server_id": server_id,
                "category": "repping",
                "$or": [
                    {"action": {"$regex": "^Role assigned:"}},
                    {"action": {"$regex": "^Role removed:"}}
                ]
            }).sort([("user_id", 1), ("timestamp", 1)]))

            # Calculate total repping hours for each user
            user_hours = {}
            user_info = {}

            for event in repping_events:
                user_id = event['user_id']
                timestamp = event['timestamp']
                action = event['action']
                username = event.get('username', f'User {user_id}')

                # Ensure timestamp is timezone-aware
                timestamp = ensure_timezone_aware(timestamp)

                if user_id not in user_hours:
                    user_hours[user_id] = {'total_seconds': 0, 'current_start': None}
                    user_info[user_id] = {'username': username, 'last_activity': timestamp}

                # Ensure last_activity is timezone-aware for comparison
                user_info[user_id]['last_activity'] = ensure_timezone_aware(user_info[user_id]['last_activity'])

                user_info[user_id]['last_activity'] = max(user_info[user_id]['last_activity'], timestamp)

                if 'assigned' in action:
                    # Role assigned - start counting time
                    if user_hours[user_id]['current_start'] is None:
                        user_hours[user_id]['current_start'] = timestamp
                elif 'removed' in action:
                    # Role removed - stop counting time
                    if user_hours[user_id]['current_start'] is not None:
                        # Ensure current_start is timezone-aware
                        start_time = ensure_timezone_aware(user_hours[user_id]['current_start'])

                        duration = (timestamp - start_time).total_seconds()
                        user_hours[user_id]['total_seconds'] += duration
                        user_hours[user_id]['current_start'] = None

            # Handle users who still have the role (no removal event)
            current_time = datetime.now(timezone.utc)
            for user_id in user_hours:
                if user_hours[user_id]['current_start'] is not None:
                    # Ensure current_start is timezone-aware
                    start_time = ensure_timezone_aware(user_hours[user_id]['current_start'])

                    duration = (current_time - start_time).total_seconds()
                    user_hours[user_id]['total_seconds'] += duration

            # Convert to list and sort by total hours
            result = []
            for user_id, hours_data in user_hours.items():
                total_hours = hours_data['total_seconds'] / 3600  # Convert to hours
                if total_hours > 0:  # Only include users with actual repping time
                    result.append({
                        '_id': user_id,
                        'total_hours': round(total_hours, 1),
                        'username': user_info[user_id]['username'],
                        'last_activity': user_info[user_id]['last_activity'],
                        'display_name': user_info[user_id]['username'],
                        'avatar_url': None
                    })

            # Sort by total hours and limit results
            result.sort(key=lambda x: x['total_hours'], reverse=True)
            result = result[:limit]

            # If bot is available, get live Discord user info
            if bot:
                guild = bot.get_guild(server_id)
                if guild:
                    for user_data in result:
                        try:
                            member = guild.get_member(user_data['_id'])
                            if member:
                                user_data['display_name'] = member.display_name
                                user_data['avatar_url'] = str(member.avatar.url) if member.avatar else None
                        except:
                            pass  # Keep existing fallback values

            return result

        except Exception as e:
            logger.error(f"Error calculating top repping users for server {server_id}: {e}", exc_info=True)
            return []

    def get_recent_repping_activity(self, server_id: int, limit: int = 25) -> List[Dict[str, Any]]:
        """Get recent repping activity for the server"""
        logs_collection = self.db['leakin-bot-logs']

        # Get recent repping activities
        activities = list(logs_collection.find({
            "server_id": server_id,
            "category": "repping"
        }).sort("timestamp", -1).limit(limit))

        return activities

    def get_current_repping_users(self, server_id: int, bot=None) -> List[Dict[str, Any]]:
        """Get list of users who currently have the repping role assigned (live from Discord)"""
        if not bot:
            # Fallback to log-based method if bot is not available
            return self._get_repping_users_from_logs(server_id)

        # Get server config to find the role ID
        config = self.get_server_config(server_id)
        if not config or not config.get('role_id'):
            return []

        # Get the guild and role from Discord
        guild = bot.get_guild(server_id)
        if not guild:
            return []

        role = guild.get_role(config['role_id'])
        if not role:
            return []

        # Get all members who currently have the role
        current_users = []
        for member in role.members:
            if not member.bot:  # Exclude bots
                current_users.append({
                    'user_id': member.id,
                    'username': f"{member.name}#{member.discriminator}",
                    'display_name': member.display_name,
                    'assigned_at': None  # We don't track exact assignment time for live data
                })

        return current_users

    def _get_repping_users_from_logs(self, server_id: int) -> List[Dict[str, Any]]:
        """Fallback method to get repping users from logs"""
        logs_collection = self.db['leakin-bot-logs']

        # Get users who have had role assignments more recently than removals
        pipeline = [
            {"$match": {
                "server_id": server_id,
                "category": "repping",
                "$or": [
                    {"action": {"$regex": "^Role assigned:"}},
                    {"action": {"$regex": "^Role removed:"}}
                ]
            }},
            {"$sort": {"timestamp": -1}},
            {"$group": {
                "_id": "$user_id",
                "latest_action": {"$first": "$action"},
                "latest_username": {"$first": "$username"},
                "latest_timestamp": {"$first": "$timestamp"}
            }},
            {"$match": {"latest_action": {"$regex": "^Role assigned:"}}},
            {"$project": {
                "user_id": "$_id",
                "username": "$latest_username",
                "assigned_at": "$latest_timestamp"
            }},
            {"$sort": {"assigned_at": -1}}
        ]

        return list(logs_collection.aggregate(pipeline))

    def get_active_temp_channels_with_users(self, server_id: int) -> List[Dict[str, Any]]:
        """Get active temp voice channels with owner information"""
        collection = self.db['leakin-temp-channels']

        pipeline = [
            {"$match": {
                "server_id": server_id,
                "active": True
            }},
            {"$sort": {"created_at": -1}}
        ]

        channels = list(collection.aggregate(pipeline))

        # Get usernames from logs for each owner
        logs_collection = self.db['leakin-bot-logs']
        for channel in channels:
            # Try to get the most recent username for this user
            user_log = logs_collection.find_one(
                {"server_id": server_id, "user_id": channel['owner_id']},
                sort=[("timestamp", -1)]
            )
            channel['owner_username'] = user_log['username'] if user_log else f"User#{channel['owner_id']}"

        return channels

    # ========== GIVEAWAY METHODS ==========

    def create_giveaway(self, server_id: int, channel_id: int, host_user_id: int,
                       item: str, requirements: str, winners: int, end_time: datetime,
                       admin_winner_ids: list = None) -> str:
        """Create a new giveaway"""
        collection = self.db['leakin-giveaways']

        # Ensure end_time is timezone-aware (assume UTC if naive)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)
            logger.info(f"Converted naive datetime to UTC: {end_time}")

        # Ensure created_at is timezone-aware
        created_at = datetime.now(timezone.utc)

        giveaway_data = {
            'server_id': server_id,
            'channel_id': channel_id,
            'host_user_id': host_user_id,
            'item': item,
            'requirements': requirements,
            'winners': winners,
            'end_time': end_time,
            'created_at': created_at,
            'message_id': None,  # Will be set when message is sent
            'entries': [],  # List of user IDs who entered
            'active': True,
            'ended': False,
            'winners_selected': []
        }

        # Add admin winner IDs if provided (for admin override)
        if admin_winner_ids:
            giveaway_data['admin_winner_ids'] = admin_winner_ids

        result = collection.insert_one(giveaway_data)
        return str(result.inserted_id)

    def update_giveaway_message_id(self, giveaway_id: str, message_id: int) -> bool:
        """Update the message ID for a giveaway"""
        collection = self.db['leakin-giveaways']

        result = collection.update_one(
            {"_id": ObjectId(giveaway_id)},
            {"$set": {"message_id": message_id}}
        )

        return result.modified_count > 0

    def enter_giveaway(self, giveaway_id: str, user_id: int) -> bool:
        """Enter a user into a giveaway"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']

            # Check if user is already entered
            giveaway = collection.find_one({"_id": ObjectId(giveaway_id)})
            if not giveaway or not giveaway.get('active'):
                logger.warning(f"Giveaway {giveaway_id} not found or not active")
                return False

            if user_id in giveaway.get('entries', []):
                logger.info(f"User {user_id} already in giveaway {giveaway_id}")
                return False  # Already entered

            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$addToSet": {"entries": user_id}}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"User {user_id} entered giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to enter user {user_id} into giveaway {giveaway_id}")
                
            return success
        except Exception as e:
            logger.error(f"Error in enter_giveaway for user {user_id}, giveaway {giveaway_id}: {e}")
            return False

    def leave_giveaway(self, giveaway_id: str, user_id: int) -> bool:
        """Remove a user from a giveaway"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$pull": {"entries": user_id}}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"User {user_id} left giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to remove user {user_id} from giveaway {giveaway_id}")
                
            return success
        except Exception as e:
            logger.error(f"Error in leave_giveaway for user {user_id}, giveaway {giveaway_id}: {e}")
            return False

    def get_giveaway(self, giveaway_id: str) -> Dict[str, Any]:
        """Get a giveaway by ID"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            giveaway = collection.find_one({"_id": ObjectId(giveaway_id)})
            if not giveaway:
                return None
                
            # Convert ObjectId to string
            giveaway['_id'] = str(giveaway['_id'])
            
            # Ensure datetime fields are timezone-aware
            if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                
            if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                
            return giveaway
            
        except Exception as e:
            logger.error(f"Error getting giveaway {giveaway_id}: {e}", exc_info=True)
            return None

    def get_giveaway_by_message(self, message_id: int) -> Dict[str, Any]:
        """Get a giveaway by message ID"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            giveaway = collection.find_one({"message_id": message_id})
            if not giveaway:
                return None
                
            # Convert ObjectId to string
            giveaway['_id'] = str(giveaway['_id'])
            
            # Ensure datetime fields are timezone-aware
            if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                
            if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                
            return giveaway
            
        except Exception as e:
            logger.error(f"Error getting giveaway by message {message_id}: {e}", exc_info=True)
            return None

    def get_active_giveaways(self, server_id: int = None) -> List[Dict[str, Any]]:
        """Get all active giveaways, optionally filtered by server"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']

            query = {"active": True, "ended": False}
            if server_id:
                query["server_id"] = server_id

            giveaways = list(collection.find(query))
            
            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                try:
                    # Convert ObjectId to string
                    giveaway['_id'] = str(giveaway['_id'])
                    
                    # Ensure datetime fields are timezone-aware
                    if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                        giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                        
                    if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                        giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                        
                    processed_giveaways.append(giveaway)
                    
                except Exception as e:
                    logger.error(f"Error processing giveaway {giveaway.get('_id')}: {e}", exc_info=True)
                    continue

            return processed_giveaways
            
        except Exception as e:
            logger.error(f"Error getting active giveaways: {e}", exc_info=True)
            return []

            
    def leave_giveaway(self, giveaway_id: str, user_id: int) -> bool:
        """Remove a user from a giveaway"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$pull": {"entries": user_id}}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"User {user_id} left giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to remove user {user_id} from giveaway {giveaway_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error in leave_giveaway for user {user_id}, giveaway {giveaway_id}: {e}", exc_info=True)
            return False

            
    def get_giveaway_by_message(self, message_id: int) -> Dict[str, Any]:
        """Get a giveaway by message ID"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            giveaway = collection.find_one({"message_id": message_id})
            if not giveaway:
                return None
                
            # Convert ObjectId to string
            giveaway['_id'] = str(giveaway['_id'])
            
            # Ensure datetime fields are timezone-aware
            if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                
            if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                
            return giveaway
            
        except Exception as e:
            logger.error(f"Error getting giveaway by message ID {message_id}: {e}")
            return None
            
    def get_all_active_giveaways(self) -> List[Dict[str, Any]]:
        """Get all active giveaways across all servers"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']

            # Find all active giveaways
            giveaways = list(collection.find({
                "active": True,
                "ended": False
            }))

            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                # Convert ObjectId to string
                giveaway['_id'] = str(giveaway['_id'])

                # Ensure datetime fields are timezone-aware
                if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                    giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)

                if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                    giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)

                processed_giveaways.append(giveaway)

            return processed_giveaways

        except Exception as e:
            logger.error(f"Error getting all active giveaways: {e}", exc_info=True)
            return []

    def get_expired_giveaways(self) -> List[Dict[str, Any]]:
        """Get all giveaways that have expired but not ended"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            now = datetime.now(timezone.utc)

            # Find giveaways that are active, not ended, and have end_time <= now
            giveaways = list(collection.find({
                "active": True,
                "ended": False,
                "end_time": {"$lte": now}
            }))

            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                try:
                    # Convert ObjectId to string
                    giveaway['_id'] = str(giveaway['_id'])
                    
                    # Ensure datetime fields are timezone-aware
                    if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                        giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                        
                    if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                        giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                        
                    processed_giveaways.append(giveaway)
                    
                except Exception as e:
                    logger.error(f"Error processing expired giveaway {giveaway.get('_id')}: {e}", exc_info=True)
                    continue

            return processed_giveaways

        except Exception as e:
            logger.error(f"Error getting expired giveaways: {e}", exc_info=True)
            return []

    def delete_giveaway(self, giveaway_id: str) -> bool:
        """Delete a giveaway from the database"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            result = collection.delete_one({"_id": ObjectId(giveaway_id)})

            success = result.deleted_count > 0
            if success:
                logger.info(f"Deleted giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to delete giveaway {giveaway_id} - not found")

            return success

        except Exception as e:
            logger.error(f"Error deleting giveaway {giveaway_id}: {e}", exc_info=True)
            return False

    def update_giveaway_winners(self, giveaway_id: str, winners: list) -> bool:
        """Update the winners for a giveaway (for rerolling)"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$set": {"winners_selected": winners}}
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Updated winners for giveaway {giveaway_id}: {winners}")
            else:
                logger.warning(f"Failed to update winners for giveaway {giveaway_id}")

            return success

        except Exception as e:
            logger.error(f"Error updating giveaway winners {giveaway_id}: {e}", exc_info=True)
            return False
            
    def end_giveaway(self, giveaway_id: str, winners: List[int]) -> bool:
        """End a giveaway and set winners"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$set": {
                    "ended": True,
                    "active": False,
                    "winners_selected": winners,
                    "ended_at": datetime.now(timezone.utc)
                }}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"Ended giveaway {giveaway_id} with {len(winners)} winners")
            else:
                logger.warning(f"Failed to end giveaway {giveaway_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error ending giveaway {giveaway_id}: {e}", exc_info=True)
            return False

        return result.modified_count > 0

    def get_server_giveaways(self, server_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get giveaways for a server with proper timezone handling"""
        self._ensure_connected()
        try:
            collection = self.db['leakin-giveaways']
            
            # Get giveaways for the server, sorted by creation date (newest first)
            giveaways = list(collection.find(
                {"server_id": server_id}
            ).sort("created_at", -1).limit(limit))
            
            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                try:
                    # Convert ObjectId to string
                    giveaway['_id'] = str(giveaway['_id'])
                    
                    # Ensure datetime fields are timezone-aware
                    if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                        giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                        
                    if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                        giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                        
                    processed_giveaways.append(giveaway)
                    
                except Exception as e:
                    logger.error(f"Error processing giveaway {giveaway.get('_id')}: {e}", exc_info=True)
                    continue

            return processed_giveaways
            
        except Exception as e:
            logger.error(f"Error getting giveaways for server {server_id}: {e}", exc_info=True)
            return []

    def cleanup_all_server_logs(self, server_id: int, max_logs: int = 100) -> int:
        """Manually clean up logs for a server and return number of deleted logs"""
        collection = self.db['leakin-bot-logs']

        try:
            # Count current logs
            total_logs = collection.count_documents({"server_id": server_id})

            if total_logs <= max_logs:
                return 0

            # Calculate how many to delete
            logs_to_delete = total_logs - max_logs

            # Get the oldest logs
            oldest_logs = list(collection.find(
                {"server_id": server_id}
            ).sort("timestamp", 1).limit(logs_to_delete))

            if oldest_logs:
                # Delete the oldest logs
                oldest_ids = [log["_id"] for log in oldest_logs]
                result = collection.delete_many({"_id": {"$in": oldest_ids}})

                logger.info(f"Manual cleanup: Deleted {result.deleted_count} old log entries for server {server_id}")
                return result.deleted_count

            return 0

        except Exception as e:
            logger.error(f"Error in manual log cleanup for server {server_id}: {e}")
            return 0

    # DM Support System Methods
    def set_dm_support_settings(self, server_id: int, category_id: int, support_role_id: int, logs_channel_id: int) -> bool:
        """Save DM support settings for a server"""
        collection = self.db['leakin-dm-support-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "category_id": category_id,
                "support_role_id": support_role_id,
                "logs_channel_id": logs_channel_id,
                "enabled": True,
                "updated_at": datetime.now(timezone.utc)
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_dm_support_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get DM support settings for a server"""
        collection = self.db['leakin-dm-support-settings']
        return collection.find_one({"server_id": server_id})

    def update_dm_support_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update DM support system enabled status"""
        collection = self.db['leakin-dm-support-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0

    def get_user_servers_with_dm_support(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all servers where user is a member and DM support is enabled"""
        collection = self.db['leakin-dm-support-settings']
        return list(collection.find({"enabled": True}))

    def create_dm_support_ticket(self, server_id: int, user_id: int, initial_message: str) -> str:
        """Create a new DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        # Check if user already has an open ticket for this server
        existing_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })

        if existing_ticket:
            return "existing"

        # Create new ticket
        ticket = {
            "server_id": server_id,
            "user_id": user_id,
            "channel_id": None,
            "status": "open",
            "initial_message": initial_message,
            "messages": [],
            "created_at": datetime.now(timezone.utc),
            "closed_at": None,
            "closed_by": None,
            "close_reason": None
        }

        result = collection.insert_one(ticket)
        return str(result.inserted_id)

    def get_user_open_dm_ticket(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open DM support ticket (globally)"""
        collection = self.db['leakin-dm-support-tickets']
        return collection.find_one({
            "user_id": user_id,
            "status": "open"
        })

    def get_dm_ticket_by_channel(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get DM support ticket by channel ID"""
        collection = self.db['leakin-dm-support-tickets']
        return collection.find_one({
            "channel_id": channel_id,
            "status": "open"
        })

    def update_dm_ticket_channel(self, ticket_id: str, channel_id: int) -> bool:
        """Update the channel ID for a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {"channel_id": channel_id}}
        )

        return result.modified_count > 0

    def add_message_to_dm_ticket(self, ticket_id: str, user_id: int, username: str, message: str, is_staff: bool = False, attachment_urls: list = None) -> bool:
        """Add a message to a DM support ticket
        
        Args:
            ticket_id: The ID of the ticket
            user_id: The ID of the user who sent the message
            username: The username of the user who sent the message
            message: The message content
            is_staff: Whether the sender is a staff member
            attachment_urls: List of attachment URLs (default: None)
        """
        collection = self.db['leakin-dm-support-tickets']

        message_doc = {
            "user_id": user_id,
            "username": username,
            "message": message,
            "is_staff": is_staff,
            "timestamp": datetime.now(timezone.utc),
            "attachments": attachment_urls or []
        }

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {
                "$push": {"messages": message_doc},
                "$set": {"updated_at": datetime.now(timezone.utc)}
            }
        )

        return result.modified_count > 0

    def close_dm_support_ticket(self, ticket_id: str, closed_by: int, reason: str) -> bool:
        """Close a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.now(timezone.utc),
                "closed_by": closed_by,
                "close_reason": reason
            }}
        )

        return result.modified_count > 0

    def get_dm_ticket_transcript(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get full transcript of a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']
        return collection.find_one({"_id": ObjectId(ticket_id)})
