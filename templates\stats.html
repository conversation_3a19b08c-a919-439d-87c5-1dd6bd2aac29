﻿{% extends "base.html" %}

{% block title %}Stats - {{ server_info.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* Progress bar styles */
.progress-thin {
    height: 4px;
}

/* Card styles */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* Dark theme support */
[data-bs-theme="dark"] .card {
    background-color: var(--bs-dark) !important;
    color: var(--bs-light) !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .card-header {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

[data-bs-theme="dark"] .list-group-item {
    background-color: transparent !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--bs-light) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Activity card height fixes */
.card.h-100 {
    min-height: 400px;
}

.card-body.d-flex.flex-column {
    height: 100%;
}

.list-group.flex-grow-1 {
    flex: 1;
    overflow-y: auto;
}

/* Stats cards height control */
.row .col-md-6.col-xl-3 .card {
    height: auto !important;
    min-height: 120px;
    max-height: 150px;
}

.row .col-md-6.col-xl-3 .card .card-body {
    padding: 1rem;
}

/* Server Insights height control */
.server-insights-card {
    height: 320px !important;
    max-height: 320px;
}

.server-insights-card .card-body {
    height: calc(100% - 60px);
    overflow: hidden;
}

/* Activity Distribution chart height */
.activity-distribution-card {
    height: 180px !important;
}

.activity-distribution-card .card-body {
    height: calc(100% - 30px);
}

#activityDistributionChart {
    max-height: 120px !important;
    height: 120px !important;
}

/* Recent Repping Activity height control */
.recent-activity-card {
    height: 320px !important;
    max-height: 320px;
}

.recent-activity-scroll {
    height: 260px;
    overflow-y: auto;
    overflow-x: hidden;
}

.recent-activity-scroll .list-group-item {
    border-left: none;
    border-right: none;
}

/* Icon shapes */
.icon-shape {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
}

/* Activity list */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

/* Progress bar width */
.progress-bar-custom {
    width: var(--progress-width, 0%);
}

/* Chart container height fix */
#activityChart {
    max-height: 300px !important;
    height: 300px !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-1">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Server Statistics
                </h1>
                <p class="text-muted mb-0">Analytics and insights for your server</p>
            </div>
            <div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Navigation</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link active" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-line"></i>Statistics
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-list-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <!-- Total Members -->
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-primary rounded p-3">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Total Members</span>
                            <h3 class="mb-0">{{ server_stats.member_count|default('0')|number_format }}</h3>
                        </div>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Online Members -->
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-success rounded p-3">
                            <i class="fas fa-signal text-success"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Online Now</span>
                            <h3 class="mb-0">{{ server_stats.online_count|default('0')|number_format }}</h3>
                        </div>
                    </div>
                    {% set progress_width = (server_stats.online_count / server_stats.member_count * 100 if server_stats.member_count and server_stats.member_count > 0 else 0)|round|int %}
                    <div class="progress progress-thin">
                        <div class="progress-bar bg-success progress-bar-custom" 
                             role="progressbar" 
                             style="--progress-width: {{ progress_width }}%" 
                             aria-valuenow="{{ progress_width }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                            <span class="visually-hidden">{{ progress_width }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Channels -->
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-info rounded p-3">
                            <i class="fas fa-hashtag text-info"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Total Channels</span>
                            <h3 class="mb-0">{{ server_stats.channel_count|default('0')|number_format }}</h3>
                        </div>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Server Boost -->
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-warning rounded p-3">
                            <i class="fas fa-gem text-warning"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Server Boosts</span>
                            <h3 class="mb-0">{{ server_stats.premium_subscription_count|default('0')|number_format }}</h3>
                        </div>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity and Repping Section -->
    <div class="row g-4 mb-4">
        <!-- Activity Overview -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line text-primary me-2"></i>Server Activity Metrics</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            Last 7 Days
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="timeRangeDropdown">
                            <li><a class="dropdown-item active" href="#" data-period="7">Last 7 Days</a></li>
                            <li><a class="dropdown-item" href="#" data-period="30">Last 30 Days</a></li>
                            <li><a class="dropdown-item" href="#" data-period="90">Last 3 Months</a></li>
                            <li><a class="dropdown-item" href="#" data-period="0">All Time</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Current Repping Users -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-star text-warning me-2"></i>Top Repping Users</h5>
                    <span class="badge bg-warning-subtle text-warning">{{ top_repping_users|length }} Users</span>
                </div>
                <div class="card-body p-0">
                    {% if top_repping_users %}
                        <div class="list-group list-group-flush">
                            {% for user in top_repping_users %}
                                <div class="list-group-item list-group-item-action d-flex align-items-center">
                                    <img src="{{ user.avatar_url or 'https://cdn.discordapp.com/embed/avatars/' + (user._id % 5)|string + '.png' }}"
                                         class="rounded-circle me-3"
                                         width="40"
                                         height="40"
                                         onerror="this.src='https://cdn.discordapp.com/embed/avatars/{{ range(0, 5)|random }}.png';"
                                         alt="{{ user.display_name }}">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ user.display_name }}</h6>
                                        <small class="text-muted">{{ user.total_hours }} hours repping</small>
                                    </div>
                                    <span class="badge bg-light text-dark">{{ user.last_activity|time_ago }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted p-4">
                            <div class="mb-3">
                                <i class="fas fa-star fa-3x text-warning opacity-25"></i>
                            </div>
                            <h6 class="mb-1">No Active Reps</h6>
                            <p class="small mb-0">Users will appear here when they start repping</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="row g-4">
        <!-- Recent Repping Activity -->
        <div class="col-lg-6">
            <div class="card recent-activity-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history text-primary me-2"></i>Recent Repping Activity</h5>
                    <span class="badge bg-primary-subtle text-primary">{{ recent_repping_activity|length }} Events</span>
                </div>
                <div class="card-body p-0">
                    {% if recent_repping_activity %}
                        <div class="list-group list-group-flush recent-activity-scroll">
                            {% for activity in recent_repping_activity %}
                                <div class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">{{ activity.username or 'User ' + activity.user_id|string }}</h6>
                                                <small class="text-muted">{{ activity.timestamp|time_ago }}</small>
                                            </div>
                                            <p class="mb-0 text-muted small">
                                                {% if 'assigned' in activity.action %}
                                                    <i class="fas fa-plus-circle text-success me-1"></i>{{ activity.action }}
                                                {% else %}
                                                    <i class="fas fa-minus-circle text-danger me-1"></i>{{ activity.action }}
                                                {% endif %}
                                                {% if activity.reason %}
                                                    <span class="d-block text-truncate mt-1">{{ activity.reason }}</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted p-4">
                            <div class="mb-3">
                                <i class="fas fa-inbox fa-3x text-muted opacity-25"></i>
                            </div>
                            <h6 class="mb-1">No Recent Activity</h6>
                            <p class="small mb-0">Recent repping activity will appear here</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Server Insights -->
        <div class="col-lg-6">
            <div class="card server-insights-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie text-info me-2"></i>Server Insights</h5>
                </div>
                <div class="card-body p-3">
                    <div class="row g-2">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center p-2">
                                    <div class="icon-shape bg-primary bg-opacity-10 text-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <h5 class="mb-0">{{ server_stats.total_actions|default('0')|number_format }}</h5>
                                    <p class="text-muted small mb-0">Total Actions</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center p-2">
                                    <div class="icon-shape bg-success bg-opacity-10 text-success rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h5 class="mb-0">{{ server_stats.active_users|default('0')|number_format }}</h5>
                                    <p class="text-muted small mb-0">Active Users (30 days)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card bg-light activity-distribution-card">
                                <div class="card-body p-2">
                                    <h6 class="text-uppercase text-muted small fw-bold mb-2">Activity Distribution</h6>
                                    <canvas id="activityDistributionChart" height="120"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div> <!-- Close main content -->
</div> <!-- Close row -->
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Activity Chart with real data
const activityCtx = document.getElementById('activityChart').getContext('2d');
let activityChart;

// Initialize chart with server data
function initActivityChart(logStats) {
    // Prepare datasets for multiple activity types
    const dates = logStats.daily_activity ? Object.keys(logStats.daily_activity).sort() : [];
    const dailyByCategory = logStats.daily_by_category || {};

    // Define colors for specific metrics
    const metricColors = {
        'users_repping': { border: 'rgba(255, 193, 7, 1)', bg: 'rgba(255, 193, 7, 0.1)' },
        'voice_channels_created': { border: 'rgba(32, 201, 151, 1)', bg: 'rgba(32, 201, 151, 0.1)' },
        'messages_sent': { border: 'rgba(13, 110, 253, 1)', bg: 'rgba(13, 110, 253, 0.1)' },
        'messages_deleted': { border: 'rgba(220, 53, 69, 1)', bg: 'rgba(220, 53, 69, 0.1)' }
    };

    // Define metric labels
    const metricLabels = {
        'users_repping': 'Users Repping',
        'voice_channels_created': 'Voice Channels Created',
        'messages_sent': 'Messages Sent',
        'messages_deleted': 'Messages Deleted'
    };

    // Create datasets for each metric
    const datasets = [];
    const metrics = new Set();

    // Collect all metrics
    Object.values(dailyByCategory).forEach(dayData => {
        Object.keys(dayData).forEach(metric => metrics.add(metric));
    });

    // Create dataset for each metric
    metrics.forEach(metric => {
        const data = dates.map(date => {
            return dailyByCategory[date] && dailyByCategory[date][metric] ? dailyByCategory[date][metric] : 0;
        });

        const colors = metricColors[metric] || {
            border: 'rgba(108, 117, 125, 1)',
            bg: 'rgba(108, 117, 125, 0.1)'
        };

        datasets.push({
            label: metricLabels[metric] || metric.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            data: data,
            borderColor: colors.border,
            backgroundColor: colors.bg,
            tension: 0.3,
            fill: false
        });
    });

    // If no metric data, fall back to overall activity
    if (datasets.length === 0) {
        datasets.push({
            label: 'Total Bot Activity',
            data: logStats.daily_activity ? Object.values(logStats.daily_activity) : [],
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.3,
            fill: true
        });
    }

    const data = {
        labels: dates,
        datasets: datasets
    };

    activityChart = new Chart(activityCtx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Initialize chart with server data
const initialLogStats = {{ log_stats|tojson }};
initActivityChart(initialLogStats);

// Time period dropdown functionality
document.querySelectorAll('[data-period]').forEach(item => {
    item.addEventListener('click', function(e) {
        e.preventDefault();
        const period = this.getAttribute('data-period');
        const text = this.textContent;

        // Update dropdown button text
        document.getElementById('timeRangeDropdown').textContent = text;

        // Remove active class from all items
        document.querySelectorAll('.dropdown-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked item
        this.classList.add('active');

        // Fetch new data
        fetch(`/api/stats-data?period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Destroy existing chart and recreate with new data
                    activityChart.destroy();
                    initActivityChart(data.log_stats);
                }
            })
            .catch(error => console.error('Error fetching stats data:', error));
    });
});

// Activity Distribution Chart with real data
const distCtx = document.getElementById('activityDistributionChart').getContext('2d');

// Prepare distribution data from server stats
const categoryStats = initialLogStats.category_stats || {};
const labels = Object.keys(categoryStats).filter(key => key && categoryStats[key] > 0);
const data = labels.map(key => categoryStats[key]);

const colors = [
    'rgba(255, 193, 7, 0.8)',
    'rgba(32, 201, 151, 0.8)',
    'rgba(13, 110, 253, 0.8)',
    'rgba(220, 53, 69, 0.8)',
    'rgba(108, 117, 125, 0.8)',
    'rgba(255, 99, 132, 0.8)',
    'rgba(255, 159, 64, 0.8)',
    'rgba(153, 102, 255, 0.8)'
];

const distChart = new Chart(distCtx, {
    type: 'doughnut',
    data: {
        labels: labels.length > 0 ? labels.map(label => label.charAt(0).toUpperCase() + label.slice(1)) : ['No Activity'],
        datasets: [{
            data: data.length > 0 ? data : [1],
            backgroundColor: labels.length > 0 ? colors.slice(0, labels.length) : ['rgba(200, 200, 200, 0.8)'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '60%',
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    font: {
                        size: 11
                    },
                    padding: 8
                }
            }
        }
    }
});

// Update time every second
function updateTime() {
    const now = new Date();
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = now.toLocaleString();
    }
}

// Initial time update
updateTime();
setInterval(updateTime, 1000);
</script>
{% endblock %}
