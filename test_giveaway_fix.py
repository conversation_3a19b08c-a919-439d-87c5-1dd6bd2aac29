#!/usr/bin/env python3
"""
Test script to verify giveaway interaction fix
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from main import PersistentGiveawayView, create_giveaway_embed

def test_giveaway_components():
    """Test that giveaway components are working correctly"""
    print("🧪 Testing giveaway fix components...")
    
    # Test 1: Check if PersistentGiveawayView can be instantiated
    try:
        view = PersistentGiveawayView()
        print("✅ PersistentGiveawayView instantiated successfully")
    except Exception as e:
        print(f"❌ Failed to instantiate PersistentGiveawayView: {e}")
        return False
    
    # Test 2: Check if database methods exist
    try:
        db = DatabaseManager()
        
        # Check if get_giveaway_by_message method exists
        if hasattr(db, 'get_giveaway_by_message'):
            print("✅ get_giveaway_by_message method exists")
        else:
            print("❌ get_giveaway_by_message method missing")
            return False
            
        # Check if enter_giveaway method exists
        if hasattr(db, 'enter_giveaway'):
            print("✅ enter_giveaway method exists")
        else:
            print("❌ enter_giveaway method missing")
            return False
            
        # Check if leave_giveaway method exists
        if hasattr(db, 'leave_giveaway'):
            print("✅ leave_giveaway method exists")
        else:
            print("❌ leave_giveaway method missing")
            return False
            
    except Exception as e:
        print(f"❌ Database method check failed: {e}")
        return False
    
    # Test 3: Check if create_giveaway_embed includes ID in footer
    try:
        from datetime import datetime, timezone
        
        test_giveaway = {
            '_id': '507f1f77bcf86cd799439011',
            'item': 'Test Prize',
            'requirements': 'Test requirement',
            'end_time': datetime.now(timezone.utc),
            'host_user_id': 123456789,
            'entries': [111, 222, 333],
            'winners': 1
        }
        
        embed = create_giveaway_embed(test_giveaway)
        
        if embed.footer and embed.footer.text and 'ID: 507f1f77bcf86cd799439011' in embed.footer.text:
            print("✅ Giveaway embed includes ID in footer")
        else:
            print(f"❌ Giveaway embed footer missing ID. Footer: {embed.footer.text if embed.footer else 'None'}")
            return False
            
    except Exception as e:
        print(f"❌ Embed test failed: {e}")
        return False
    
    print("🎉 All giveaway fix tests passed!")
    return True

if __name__ == "__main__":
    success = test_giveaway_components()
    sys.exit(0 if success else 1)
